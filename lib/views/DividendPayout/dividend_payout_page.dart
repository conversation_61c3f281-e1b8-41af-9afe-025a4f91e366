import 'package:flutter/material.dart';
import 'package:foodcorp_admin/common/page_header.dart';
import 'package:foodcorp_admin/controller/homectrl.dart';
import 'package:foodcorp_admin/models/user_model.dart';
import 'package:foodcorp_admin/shared/firebase.dart';
import 'package:foodcorp_admin/shared/methods.dart';
import 'package:foodcorp_admin/shared/const.dart';
import 'package:get/get.dart';
import 'package:pluto_grid/pluto_grid.dart';
import 'package:csv/csv.dart';
import 'package:file_saver/file_saver.dart';
import 'dart:typed_data';

class DividendPayoutPage extends StatefulWidget {
  const DividendPayoutPage({super.key});

  @override
  State<DividendPayoutPage> createState() => _DividendPayoutPageState();
}

class _DividendPayoutPageState extends State<DividendPayoutPage> {
  late PlutoGridStateManager stateManager;

  String? dividendSelectedYear;

  bool dividendIsLoading = false;
  bool expDividendIsLoading = false;

  bool hasShownNoDividendSnackbar = false;
  bool hasInsertedDividendRows = false;

  List<String> yearList = TheFinancialYear.generateFinancialYearsList();

  List<PlutoRow> rows = [];

  @override
  void initState() {
    super.initState();
    yearList = TheFinancialYear.generateFinancialYearsList();
    dividendSelectedYear = TheFinancialYear.getCurrentFinancialYear();
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<HomeCtrl>(builder: (ctrl) {
      if (ctrl.settings?.dividentRate == null) {
        return const Center(child: CircularProgressIndicator());
      }

      WidgetsBinding.instance.addPostFrameCallback((_) {
        dividendInsertRows(ctrl.users, ctrl);
        hasInsertedDividendRows = true;
      });

      final List<PlutoColumn> columns = [
        PlutoColumn(
          title: 'SR. NO',
          field: 'sr_no',
          type: PlutoColumnType.text(),
          enableEditingMode: false,
          backgroundColor: Color(0xffC9E9D2),
        ),
        PlutoColumn(
          title: 'CPF NO.',
          field: 'cpf_no',
          type: PlutoColumnType.text(),
          enableEditingMode: false,
          backgroundColor: Color(0xffC9E9D2),
        ),
        PlutoColumn(
          title: 'NAME OF MEMBER',
          field: 'name',
          type: PlutoColumnType.text(),
          enableEditingMode: false,
          backgroundColor: Color(0xffC9E9D2),
        ),
        PlutoColumn(
          title: 'DISTRICT OFFICE',
          field: 'district_office',
          type: PlutoColumnType.text(),
          enableEditingMode: false,
          backgroundColor: Color(0xffC9E9D2),
        ),
        PlutoColumn(
          title: 'SHARE VALUE',
          field: 'share_value',
          type: PlutoColumnType.text(),
          enableEditingMode: true,
        ),
        PlutoColumn(
          title: 'DIVIDEND AMOUNT (${ctrl.settings?.dividentRate ?? 0}%)',
          field: 'dividend_amount',
          type: PlutoColumnType.text(),
          enableEditingMode: true,
        ),
        PlutoColumn(
          title: 'Status',
          field: 'status',
          type: PlutoColumnType.text(),
          enableEditingMode: true,
        ),
      ];

      return Padding(
        padding: const EdgeInsets.only(top: 40, left: 15, right: 15),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                DropdownButtonHideUnderline(
                    child: DropdownButtonFormField(
                  focusColor: Colors.transparent,
                  dropdownColor: Colors.white,
                  value: dividendSelectedYear,
                  decoration: InputDecoration(
                      hintText: "Select Year",
                      constraints:
                          const BoxConstraints(maxWidth: 150, maxHeight: 45),
                      border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(5))),
                  items: List.generate(
                    yearList.length,
                    (index) {
                      return DropdownMenuItem(
                        value: yearList[index],
                        child: Text(yearList[index].toString()),
                      );
                    },
                  ),
                  onChanged: (value) async {
                    setState(() {
                      dividendSelectedYear = value;
                      hasInsertedDividendRows = true;
                      hasShownNoDividendSnackbar = false;
                      rows.clear();
                    });

                    dividendInsertRows(ctrl.users, ctrl);
                    stateManager.removeAllRows();
                  },
                )),
                Text("Total Members: ${ctrl.users.length}"),
                Text(
                    "Total Shares: ${ctrl.users.isNotEmpty ? ctrl.users.map((e) => e.totalShares ?? 0).reduce((a, b) => a + b) : 0}"),
                Text(
                    "Total Dividend: ${ctrl.users.isNotEmpty ? ctrl.users.map((e) => (e.totalShares ?? 0) * (num.tryParse(ctrl.settings?.dividentRate ?? '0') ?? 0) / 100).reduce((a, b) => a + b) : 0}"),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    expDividendIsLoading
                        ? CircularProgressIndicator()
                        : CustomHeaderButton(
                            onPressed: () async {
                              await exportToCSV();
                            },
                            buttonName: 'Export to CSV',
                          ),
                    SizedBox(width: 5),
                    dividendIsLoading
                        ? CircularProgressIndicator()
                        : CustomHeaderButton(
                            onPressed: () async {
                              if (rows.every((row) =>
                                  row.cells['status']?.value == 'Paid')) {
                                showCtcAppSnackBar(context,
                                    "Payout Already Done for this year");
                                return;
                              }
                              bool isLoading = false;
                              await showDialog(
                                context: context,
                                barrierDismissible: false,
                                builder: (context) {
                                  return StatefulBuilder(
                                    builder: (context, setState) {
                                      return AlertDialog(
                                        title: Text('Confirm Payout'),
                                        content: Text(
                                            'Are you sure you want to proceed with the payout?'),
                                        actions: [
                                          TextButton(
                                            onPressed: isLoading
                                                ? null
                                                : () =>
                                                    Navigator.of(context).pop(),
                                            child: Text('No'),
                                          ),
                                          TextButton(
                                            onPressed: isLoading
                                                ? null
                                                : () async {
                                                    setState(
                                                        () => isLoading = true);
                                                    dividendPayoutOnPressed(
                                                        ctrl);

                                                    setState(() =>
                                                        isLoading = false);
                                                    Navigator.of(context).pop();
                                                  },
                                            child: isLoading
                                                ? CircularProgressIndicator()
                                                : Text('Yes'),
                                          ),
                                        ],
                                      );
                                    },
                                  );
                                },
                              );
                            },
                            buttonName: 'PAY OUT')
                  ],
                ),
              ],
            ),
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 22),
              child: Text("DIVIDEND PAYOUT FOR THE YEAR $dividendSelectedYear",
                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16)),
            ),
            Expanded(
              child: Container(
                decoration: BoxDecoration(
                    border: Border.all(color: Colors.transparent)),
                child: PlutoGrid(
                  columns: columns,
                  rows: rows,
                  onLoaded: (PlutoGridOnLoadedEvent event) {
                    stateManager = event.stateManager;
                    stateManager.setShowColumnFilter(true);
                    stateManager.notifyListeners();
                    stateManager.notifyListenersOnPostFrame();
                  },
                  onChanged: (PlutoGridOnChangedEvent event) {},
                  configuration: const PlutoGridConfiguration(
                      columnSize: PlutoGridColumnSizeConfig(
                          autoSizeMode: PlutoAutoSizeMode.equal),
                      scrollbar: PlutoGridScrollbarConfig(
                          draggableScrollbar: true,
                          isAlwaysShown: true,
                          scrollbarThickness:
                              PlutoScrollbar.defaultThicknessWhileDragging)),
                ),
              ),
            ),
          ],
        ),
      );
    });
  }

  void dividendPayoutOnPressed(HomeCtrl ctrl) async {
    setState(() {
      dividendIsLoading = true;
    });

    try {
      final now = DateTime.now();
      int financialYear = now.month >= 4 ? now.year : now.year - 1;

      final marchRecoverySnapshot = await FBFireStore.recoverymonthly
          .where('selectedyear', isEqualTo: financialYear)
          .where('selectedmonth', isEqualTo: 3)
          .limit(1)
          .get();

      // if (marchRecoverySnapshot.docs.isEmpty) {
      //   showCtcAppSnackBar(
      //       context, "March recovery not completed. Payout not allowed.");
      //   setState(() {
      //     dividendIsLoading = false;
      //   });
      //   return;
      // }

      for (final row in rows) {
        final user = ctrl.users.firstWhere(
          (element) =>
              element.cpfNo.toString() == row.cells['cpf_no']?.value.toString(),
        );
        // final int currentYear = DateTime.now().year;

        final querySnapshot = await FBFireStore.societyYearly
            .where('selectedyear', isEqualTo: financialYear)
            .limit(1)
            .get();

        final batch = FBFireStore.fb.batch();

        if (user.dividendPayoutStatus == true) {
          setState(() {
            dividendIsLoading = false;
          });
          return;
        } else {
          batch.update(FBFireStore.users.doc(user.docId), {
            'dividendPayoutStatus': true,
          });

          batch.update(
              FBFireStore.societyYearly.doc(querySnapshot.docs.first.id), {
            'totalDividend':
                num.tryParse(row.cells['dividend_amount']?.value ?? '0') ?? 0,
            'updatedAt': DateTime.now(),
          });

          batch.set(FBFireStore.transactions.doc(), {
            'title': 'Dividend Payout',
            'amount':
                num.tryParse(row.cells['dividend_amount']?.value ?? '0') ?? 0,
            'uId': user.docId,
            'createdAt': DateTime.now(),
            'inn': true,
            'userMonthlyId': null,
            'recoveryId': null,
          });

          batch.set(FBFireStore.notifications.doc(), {
            'title': 'Dividend Payout',
            'desc':
                '₹${num.tryParse(row.cells['dividend_amount']?.value ?? '0') ?? 0}',
            'uId': user.docId,
            'type': 'Dividend Payout',
            'districtOffice': user.districtoffice,
            'createdAt': DateTime.now(),
          });
        }

        await batch.commit();

        setState(() {
          row.cells['dividend_amount']?.value = '0';
          row.cells['status']?.value = 'Paid';
        });
      }
      showCtcAppSnackBar(context, "Payout Successful");
    } catch (e) {
      debugPrint("Error during payout: ${e.toString()}");
      showCtcAppSnackBar(context, "Payout Failed");
    } finally {
      setState(() {
        dividendIsLoading = false;
      });
    }
  }

  void dividendInsertRows(List<UserModel> users, HomeCtrl ctrl) {
    if (dividendSelectedYear == null) return;

    final selectedStartYear = int.parse(dividendSelectedYear!.split('-')[0]);

    final filteredUsers = users.where((user) {
      if (user.registrationDate == null || !user.approved) return false;
      return user.registrationDate!.year <= selectedStartYear;
    }).toList();

    if (filteredUsers.isEmpty) {
      rows.clear();

      if (!hasShownNoDividendSnackbar) {
        hasShownNoDividendSnackbar = true;
        WidgetsBinding.instance.addPostFrameCallback((_) {
          showCtcAppSnackBar(
              context, "No users available for the selected year");
        });
      }
      return;
    }

    hasShownNoDividendSnackbar = false;
    rows.clear();

    for (int i = 0; i < filteredUsers.length; i++) {
      final user = filteredUsers[i];
      final doName = ctrl.districtoffice.firstWhereOrNull(
        (element) => element.docId == user.districtoffice,
      );

      num dividendIntRate =
          num.tryParse(ctrl.settings?.dividentRate ?? '') ?? 0;

      final dividendAmount = (user.totalShares ?? 0) * dividendIntRate / 100;

      rows.add(PlutoRow(cells: {
        'sr_no': PlutoCell(value: '${i + 1}'),
        'cpf_no': PlutoCell(value: user.cpfNo),
        'name': PlutoCell(value: user.name),
        'district_office': PlutoCell(value: doName?.name ?? '0'),
        'share_value': PlutoCell(value: user.totalShares?.toString() ?? '0'),
        'dividend_amount': PlutoCell(value: dividendAmount.toStringAsFixed(2)),
        'status': PlutoCell(
          value: user.dividendPayoutStatus == true ? 'Paid' : 'Unpaid',
        ),
      }));
    }

    setState(() {});
  }

  Future<void> exportToCSV() async {
    setState(() {
      expDividendIsLoading = true;
    });
    try {
      if (dividendSelectedYear == null) {
        showCtcAppSnackBar(context, "Please select a year to export");
        setState(() {
          expDividendIsLoading = false;
        });
        return;
      }
      if (rows.isEmpty) {
        showCtcAppSnackBar(context, "No data available to export");
        setState(() {
          expDividendIsLoading = false;
        });
        return;
      }
      setState(() {
        expDividendIsLoading = true;
      });

      List<List<dynamic>> csvData = [
        [
          'SR. NO',
          'CPF NO.',
          'NAME OF MEMBER',
          'DISTRICT OFFICE',
          'SHARE VALUE',
          'DIVIDEND AMOUNT',
          'STATUS',
        ],
        ...rows.map((row) => [
              row.cells['sr_no']?.value ?? '',
              row.cells['cpf_no']?.value ?? '',
              row.cells['name']?.value ?? '',
              row.cells['district_office']?.value ?? '',
              row.cells['share_value']?.value ?? '',
              row.cells['dividend_amount']?.value ?? '',
              row.cells['status']?.value ?? '',
            ])
      ];

      String csv = const ListToCsvConverter().convert(csvData);
      final bytes = Uint8List.fromList(csv.codeUnits);

      await FileSaver.instance.saveFile(
        name: 'dividend_payout_${dividendSelectedYear ?? ''}.csv',
        bytes: bytes,
        // ext: 'csv',
        mimeType: MimeType.csv,
      );
      showCtcAppSnackBar(context, "CSV exported successfully");
    } catch (e) {
      debugPrint(e.toString());
      showCtcAppSnackBar(context, "CSV export failed: ${e.toString()}");
    } finally {
      setState(() {
        expDividendIsLoading = false;
      });
    }
  }
}
