const { onCall } = require("firebase-functions/v2/https");
const { onRequest } = require("firebase-functions/v2/https");
const functions = require("firebase-functions");
// const logger = require("firebase-functions/logger");
const admin = require("firebase-admin");
const nodemailer = require('nodemailer');
const { FieldValue } = require("firebase-admin/firestore");
const {
    onDocumentWritten,
    // onDocumentCreated,
    // onDocumentUpdated,
    // onDocumentDeleted,
    // Change,
    // FirestoreEvent
} = require("firebase-functions/v2/firestore");
// const dateFuncs = require('date-and-time');
admin.initializeApp();
const db = admin.firestore();
const auth = admin.auth();
const messaging = admin.messaging();

// Create and deploy your first functions
// https://firebase.google.com/docs/functions/get-started

// exports.helloWorld = onRequest((request, response) => {
//   logger.info("Hello logs!", {structuredData: true});
//   response.send("Hello from Firebase!");
// });


var transporter = nodemailer.createTransport({
    host: 'smtp.gmail.com',
    port: 465,
    secure: true,
    auth: {
        user: '<EMAIL>',
        pass: 'skfbnyjbyotmfvmw'
    }
});

exports.getUserUidByEmail = functions.https.onCall(async (data, context) => {
  const email = data.email;
  if (!email) {
    throw new functions.https.HttpsError('invalid-argument', 'Email is required');
  }
  try {
    const userRecord = await admin.auth().getUserByEmail(email);
    return { uid: userRecord.uid };
  } catch (err) {
    throw new functions.https.HttpsError('not-found', 'No Auth user for that email');
  }
});


exports.createUser = onCall(async (request) => {
  try {
    console.log("IN CREATE USER");

    // Try to create user
    let userRecord;
    try {
      userRecord = await auth.createUser({
        email: request.data.email,
        password: request.data.password,
      });
      console.log("✅ New user created:", userRecord.uid);
    } catch (error) {
      // If email already in use, get existing user
      if (error.code === "auth/email-already-exists") {
        console.log("⚠️ Email already exists, fetching existing UID");
        userRecord = await auth.getUserByEmail(request.data.email);
        console.log("✅ Retrieved existing UID:", userRecord.uid);
      } else {
        console.log("❌ Error creating Firebase Auth user:", error.message);
        return {
          success: false,
          msg: error.message,
          code: error.code,
        };
      }
    }

    // Try to create Firestore user document (may already exist)
    try {
      const userDocRef = db.collection("Users").doc(userRecord.uid);

      const userDoc = await userDocRef.get();
      if (!userDoc.exists) {
        await userDocRef.create({
          name: request.data.name,
          cpfNo: request.data.cpfNo,
          districtoffice: request.data.districtoffice,
          email: request.data.email,
          phonenumber: request.data.phonenumber,
          createdAt: FieldValue.serverTimestamp(),
          settlement: request.data.settlement,
          totalSubs: request.data.totalSubs,
          totalSubsInt: request.data.totalSubsInt,
          ltLoansDue: request.data.ltLoansDue,
          stLoansDue: request.data.stLoansDue,
          totalLtLoans: request.data.totalLtLoans,
          totalStLoans: request.data.totalStLoans,
          totalLtIntPaid: request.data.totalLtIntPaid,
          totalStIntPaid: request.data.totalStIntPaid,
          totalDivident: request.data.totalDivident,
          totalShares: request.data.totalShares,
          registrationDate: FieldValue.serverTimestamp(),
          password: request.data.password,
          documents: request.data.documents,
          employeeNo: request.data.employeeNo,
          currentAddress: request.data.currentAddress,
          permanentAddress: request.data.permanentAddress,
          approved: request.data.approved,
          userPrevoiusMonthlyRecord: request.data.userPrevoiusMonthlyRecord,
          bankAcName: request.data.bankAcName,
          bankName: request.data.bankName,
          ifscCode: request.data.ifscCode,
          bankAcNo: request.data.bankAcNo,
          archived: request.data.archived,
          nomineeName: request.data.nomineeName,
          nomineeRelation: request.data.nomineeRelation,
          momento: request.data.momento,

        });
        console.log("📝 Firestore doc created for UID:", userRecord.uid);
      } else {
        console.log("◽ Firestore user doc already exists for UID:", userRecord.uid);
      }

      return { success: true, uid: userRecord.uid };
    } catch (error) {
      console.log("❌ Failed to create Firestore doc:", error.message);
      return { success: false, msg: error.message };
    }
  } catch (e) {
    console.log("❌ Unexpected error:", e.message);
    return { success: false, msg: e.message };
  }
});


exports.testCreateUser = onCall(async (request) => {
  try {
    console.log("IN TEST CREATE USER");

    let userRecord;

    // Step 1: Try to create the Auth user
    try {
      userRecord = await auth.createUser({
        email: request.data.email,
        password: request.data.password,
      });
      console.log("✅ New test user created:", userRecord.uid);
    } catch (error) {
      if (error.code === "auth/email-already-exists") {
        console.log("⚠️ Email already exists in Auth, retrieving existing user...");
        userRecord = await auth.getUserByEmail(request.data.email);
        console.log("✅ Existing test user UID fetched:", userRecord.uid);
      } else {
        console.error("❌ Error creating test auth user:", error.message);
        return {
          success: false,
          msg: error.message,
          code: error.code,
        };
      }
    }

    // Step 2: Create Firestore document in testUsers collection if missing
    try {
      const docRef = db.collection("testUsers").doc(userRecord.uid);
      const docSnap = await docRef.get();

      if (!docSnap.exists) {
        await docRef.create({
          name: request.data.name,
          cpfNo: request.data.cpfNo,
          districtoffice: request.data.districtoffice,
          email: request.data.email,
          phonenumber: request.data.phonenumber,
          createdAt: FieldValue.serverTimestamp(),
          settlement: request.data.settlement,
          totalSubs: request.data.totalSubs,
          totalSubsInt: request.data.totalSubsInt,
          ltLoansDue: request.data.ltLoansDue,
          stLoansDue: request.data.stLoansDue,
          totalLtLoans: request.data.totalLtLoans,
          totalStLoans: request.data.totalStLoans,
          totalLtIntPaid: request.data.totalLtIntPaid,
          totalStIntPaid: request.data.totalStIntPaid,
          totalDivident: request.data.totalDivident,
          totalShares: request.data.totalShares,
          registrationDate: FieldValue.serverTimestamp(),
          password: request.data.password,
          documents: request.data.documents,
          employeeNo: request.data.employeeNo,
          currentAddress: request.data.currentAddress,
          permanentAddress: request.data.permanentAddress,
          approved: request.data.approved,
          userPrevoiusMonthlyRecord: request.data.userPrevoiusMonthlyRecord,
          bankAcName: request.data.bankAcName,
          bankName: request.data.bankName,
          ifscCode: request.data.ifscCode,
          bankAcNo: request.data.bankAcNo,
          archived: request.data.archived,
          nomineeName: request.data.nomineeName,
          nomineeRelation: request.data.nomineeRelation,
          momento: request.data.momento,
        });

        console.log("📝 Firestore testUser doc created.");
      } else {
        console.log("📋 Firestore testUser doc already exists.");
      }

      // Return success with UID
      return {
        success: true,
        msg: "Test User Created or Already Exists",
        uid: userRecord.uid,
      };
    } catch (firestoreError) {
      console.error("❌ Firestore error:", firestoreError.message);
      return {
        success: false,
        msg: firestoreError.message,
      };
    }
  } catch (e) {
    console.error("❌ Unexpected error in testCreateUser:", e.message);
    return {
      success: false,
      msg: e.message,
    };
  }
});

exports.deleteUser = onCall(async (request) => {
    try {
        console.log(request.data);
        try {
            await admin.auth().deleteUser(request.data.uid);
            await admin
                .firestore()
                .collection("Users")
                .doc(request.data.uid)
                .delete()
                .then((val) => {
                    console.log(`Deleted ${request.data.uid}`);
                    return { success: true };
                });
        } catch (error) {
            console.log(`Error deleting ${request.data.uid}`);
        }
    } catch (error) {
        console.log(error);
        return { success: false };
    }
});

exports.testDeleteUser = onCall(async (request) => {
    try {
        console.log(request.data);
        try {
            await admin.auth().testDeleteUser(request.data.uid);
            await admin
                .firestore()
                .collection("testUsers")
                .doc(request.data.uid)
                .delete()
                .then((val) => {
                    console.log(`Deleted ${request.data.uid}`);
                    return { success: true };
                });
        } catch (error) {
            console.log(`Error deleting ${request.data.uid}`);
        }
    } catch (error) {
        console.log(error);
        return { success: false };
    }
});

exports.customnotifications = onDocumentWritten('customnotifications/{userId}', async (event) => {
    try {
        const data = event.data.after.data();
        console.log(data.test == true ? "Test Noti..." : "Global Noti...");
        console.log("data.topic");
        console.log(data.topic);
        const payload = {
            // topic: 'test',
            topic: data.test == true ? 'test' : data.topic,
            // topic: data.test !=null? 'test' : 'global',
            notification: {
                title: data.title,
                body: data.desc
            },
            android: {
                priority: "high",
                notification: {
                    channel_id: "fci"
                }
            },
            apns: {
                headers: {
                    'apns-priority': "10",
                },
                payload: {
                    aps: {
                        alert: {
                            title: data.title,
                            body: data.desc,
                        },
                        sound: "default",
                    }
                }
            },
            data: {
                title: data.title,
                body: data.desc
            }
        };
        messaging.send(payload).then((response) => {
            // Response is a message ID string.
            console.log('Successfully sent message:', response);
            return { success: true };
        }).catch((error) => {
            console.log('Error:', error.code);
            return { error: error.code };
        });

    } catch (error) {
        console.log(error);
    }
});

exports.testCustomnotifications = onDocumentWritten('testCustomNotifications/{userId}', async (event) => {
    try {
        const data = event.data.after.data();
        console.log(data.test == true ? "Test Noti..." : "Global Noti...");
        console.log("data.topic");
        console.log(data.topic);
        const payload = {
            // topic: 'test',
            topic: data.test == true ? 'test' : data.topic,
            // topic: data.test !=null? 'test' : 'global',
            notification: {
                title: data.title,
                body: data.desc
            },
            android: {
                priority: "high",
                notification: {
                    channel_id: "fci"
                }
            },
            apns: {
                headers: {
                    'apns-priority': "10",
                },
                payload: {
                    aps: {
                        alert: {
                            title: data.title,
                            body: data.desc,
                        },
                        sound: "default",
                    }
                }
            },
            data: {
                title: data.title,
                body: data.desc
            }
        };
        messaging.send(payload).then((response) => {
            // Response is a message ID string.
            console.log('Successfully sent message:', response);
            return { success: true };
        }).catch((error) => {
            console.log('Error:', error.code);
            return { error: error.code };
        });

    } catch (error) {
        console.log(error);
    }
});



exports.notifyUser = onDocumentWritten('notifications/{userId}', async (event) => {
    try {
        const data = event.data.after.data();
        // const data = event.data();
        var user = await admin.firestore().collection("Users")
            .doc(data.uId)
            .get();
        console.log(data.uId);
        if (user.exists) {
            console.log('user exist', user.exists);
            console.log('updating notification');
            console.log(data.title);
            console.log('updated!');
            console.log('Sending notificaion....');
            const payload = {
                tokens: user.data()['tokens'],
                notification: {
                    title: data.title,
                    body: data.desc.toString()
                },
                android: {
                    notification: {
                        channel_id: "fci"
                    }
                },
                data: {
                    title: data.title,
                    body: data.desc.toString()
                }
            };
            console.log('Sent....');
            console.log('title', data.title)
            console.log('msg', data.desc)


            messaging.sendEachForMulticast(payload).then((response) => {
                // Response is a message ID string.
                response.responses.forEach((resp, idx) => {
                    if (resp.success) {
                        console.log(`Successfully sent to token ${payload.tokens[idx]}:`, resp.messageId);
                    } else {
                        console.error(`Failed to send to token ${payload.tokens[idx]}:`, resp.error.code);
                        console.error(`Error message:`, resp.error.message);
                    }
                });
                return { success: true };
            }).catch((error) => {
                console.log(error.code)
                return { error: error.code };
            });
        }
    } catch (error) {
        console.log(error);
    }
});

exports.testNotifyUser = onDocumentWritten('testNotifications/{userId}', async (event) => {
    try {
        const data = event.data.after.data();
        // const data = event.data();
        var user = await admin.firestore().collection("testUsers")
            .doc(data.uId)
            .get();
        console.log(data.uId);
        if (user.exists) {
            console.log('user exist', user.exists);
            console.log('updating notification');
            console.log(data.title);
            console.log('updated!');
            console.log('Sending notificaion....');
            const payload = {
                tokens: user.data()['tokens'],
                notification: {
                    title: data.title,
                    body: data.desc.toString()
                },
                android: {
                    notification: {
                        channel_id: "fci"
                    }
                },
                data: {
                    title: data.title,
                    body: data.desc.toString()
                }
            };
            console.log('Sent....');
            console.log('title', data.title)
            console.log('msg', data.desc)


            messaging.sendEachForMulticast(payload).then((response) => {
                // Response is a message ID string.
                response.responses.forEach((resp, idx) => {
                    if (resp.success) {
                        console.log(`Successfully sent to token ${payload.tokens[idx]}:`, resp.messageId);
                    } else {
                        console.error(`Failed to send to token ${payload.tokens[idx]}:`, resp.error.code);
                        console.error(`Error message:`, resp.error.message);
                    }
                });
                return { success: true };
            }).catch((error) => {
                console.log(error.code)
                return { error: error.code };
            });
        }
    } catch (error) {
        console.log(error);
    }
});


exports.sendOtpEmail = onCall(async (request) => {
    const emailText = `<html>
  <body>
  <p>${request.data.otp} is your OTP. Valid for 5 minutes.</p>
  </body>
  </html>`;
    sendEmailToUser(request.data.email, "FOOD CORPORATION OF INDIA", emailText);
});


async function sendEmailToUser(to, subject, html) {
    try {
        const mailOptions = {
            from: {
                name: 'Food Corp',
                address: '<EMAIL>'
            },
            to: to,
            subject: subject,
            html: html
        };
        return transporter.sendMail(mailOptions, (error, data) => {
            if (error) {
                console.log(error)
                return
            }
            console.log("Sent!")
        });
    } catch (error) {
        console.log(error);
    }
}



exports.sendRecoveryEmail = onCall(async (request) => {
    try {
        const pdfBase64 = request.data.file;

        const email = request.data.email;

        // console.log('Received recoveryData:', recoveryData);

        const recoveryData = request.data.recoveryData;

        const selectedoffice = request.data.selectedoffice;

        const selectedMonth = request.data.selectedMonth;

        const selectedYear = request.data.selectedYear;

        const cc = request.data.cc;

        console.log('Received selectedoffice:', selectedoffice);

        console.log('Received selectedMonth:', selectedMonth);

        console.log('Received selectedYear:', selectedYear);




        await sendRecoveryEmail(email, cc, "Recovery Report for District Office", pdfBase64, recoveryData, selectedMonth, selectedYear, selectedoffice);

        return { message: 'Recovery email sent successfully!' };
    } catch (error) {
        console.log('Error in sendRecoveryEmail cloud function:', error);
        throw new Error('Failed to send recovery email');
    }
});

async function sendRecoveryEmail(to, cc, subject, pdfBase64, recoveryData, selectedMonth, selectedYear, selectedoffice) {
    try {
        let tableRows = '';
        for (let i = 0; i < recoveryData.length; i++) {
            const row = recoveryData[i];
            console.log(`row : ${row}`);
            const { cpfNo, memberName, districtOffice, total } = row;

            if (cpfNo && memberName && districtOffice && total) {
                tableRows += `
                    <tr>
                        <td>${cpfNo}</td>
                        <td>${memberName}</td>
                        <td>${districtOffice}</td>
                        <td>${total}</td>
                    </tr>
                `;
            } else {
                console.log('Missing properties in recoveryData row:', row);
            }

        }
        // recoveryData.forEach(row => {
        //     tableRows += `
        //         <tr>
        //             <td>${row.cpfNo}</td>
        //             <td>${row.memberName}</td>
        //             <td>${row.districtOffice}</td>
        //             <td>${row.total}</td>
        //         </tr>
        //     `;
        // });

        // Set up email options with PDF attachment
        const mailOptions = {
            from: {
                name: 'Food Corp',
                address: '<EMAIL>'
            },
            to: to,
            cc: cc,
            subject: `FCI Society Baroda recovery statement for the month of ${selectedMonth} - ${selectedYear} , ${selectedoffice}`,
            html: `
                <html>
                    <body>
                        <p>Dear Sir,</p>
                        <p>Please find herewith the recovery statement of FCI Emp. Society Baroda.</p>
                        <br>
                        <table border="1" cellspacing="0" cellpadding="5">
                            <thead>
                                <tr>
                                    <th>CPF NO.</th>
                                    <th>NAME OF MEMBER</th>
                                    <th>DISTRICT OFFICE</th>
                                    <th>TOTAL</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${tableRows}
                            </tbody>
                        </table>
                        <br>
                        <p>Thanking You,</p>
                        <p>FCI Emp. Co. Op. Credit Society.<br>Baroda</p>
                    </body>
                </html>`,
            attachments: [
                {
                    filename: 'recovery_report.pdf',
                    content: Buffer.from(pdfBase64, 'base64'),
                }
            ]
        };

        return transporter.sendMail(mailOptions, (error, info) => {
            if (error) {
                console.log('Error sending email:', error);
                return;
            }
            console.log('Email sent successfully!', info.response);
        });
    } catch (error) {
        console.log('Error in sendRecoveryEmail:', error);
    }
}


exports.notiToUser = onCall(async (request) => {
    sendNotificationToUser(
        request.body["title"],
        request.body["msg"],
        request.body["uid"]
    );
});

exports.testNotiToUser = onCall(async (request) => {
    sendNotificationToUser(
        request.body["title"],
        request.body["msg"],
        request.body["uid"]
    );
});

exports.notiToAdmin = onCall(async (request) => {
    sendNotificationToAdmin(request.body["title"], request.body["msg"]);
});

async function sendNotificationToUser(title, message, uid) {
    try {
        var user = await admin.firestore().collection("Users").doc(uid).get();
        if (user.exists) {
            const payload = {
                tokens: user.data()["tokens"],
                notification: {
                    title: title,
                    body: message,
                },
                android: {
                    notification: {
                        channel_id: "FOOD CORP",
                    },
                },
                data: {},
            };
            messaging
                .sendEachForMulticast(payload)
                .then((response) => {
                    console.log("Successfully sent message:", response);
                    return { success: true };
                })
                .catch((error) => {
                    return { error: error.code };
                });
        }
    } catch (error) {
        console.log(error);
    }
}

async function sendNotificationToAdmin(title, message) {
    try {
        const payload = {
            topic: "admin",
            notification: {
                title: title,
                body: message,
            },
            android: {
                notification: {
                    channel_id: "FOOD CORP",
                },
            },
            data: {},
        };
        messaging
            .send(payload)
            .then((response) => {
                // Response is a message ID string.
                console.log("Successfully sent message:", response);
                return { success: true };
            })
            .catch((error) => {
                return { error: error.code };
            });
    } catch (error) {
        console.log(error);
    }
}

// // Utility function to send email (OTP)
async function sendEmail(to, subject, htmlContent) {
    try {
        const mailOptions = {
            from: {
                name: 'Meta DC',
                address: '<EMAIL>',
            },
            to: to,
            subject: subject,
            html: htmlContent,
        };

        // Send the email
        await transporter.sendMail(mailOptions);
        console.log('Email sent to: ' + to);
    } catch (error) {
        console.error('Error sending email:', error);
    }
}