import 'package:flutter/material.dart';
import 'package:foodcorp_admin/common/page_header.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:pdf/pdf.dart';
import 'dart:html' as html;
import 'package:pluto_grid/pluto_grid.dart';

class ProfitDistributionPage extends StatefulWidget {
  const ProfitDistributionPage({super.key});

  @override
  State<ProfitDistributionPage> createState() => _ProfitDistributionPageState();
}

class _ProfitDistributionPageState extends State<ProfitDistributionPage> {
  late PlutoGridStateManager stateManager;

  bool proftDisRowsLoader = false;
  bool proftDisCSVLoader = false;
  bool proftDisPDFLoader = false;
  int? profitDisSelectedYear;

  List<int> yearList =
      List.generate(DateTime.now().year - 2025 + 1, (index) => 2025 + index);

  late List<PlutoColumn> columns;
  List<PlutoRow> rows = [];

  final List<Map<String, dynamic>> staticFields = [
    {"title": "DIVIDEND", "settingsKey": null, "editable": true},
    {"title": "RESERVE FUND", "settingsKey": null, "editable": true},
    {
      "title": "RESERVE FOR GOLDEN JUBILEE",
      "settingsKey": null,
      "editable": true
    },
    {"title": "EDUCATION FUND", "settingsKey": null, "editable": true},
    {"title": "BENEVULENT FUND", "settingsKey": null, "editable": true},
    {
      "title": "GENERAL RESERVE FUND",
      "settingsKey": "generalReservedFund",
      "editable": false
    },

    // Last 4 fields, % should come from settings and not be editable:
    {
      "title": "CHARITY FUND",
      "settingsKey": "charityPercent",
      "editable": false
    },
    {
      "title": "CO-OP PUBLICITY FUND",
      "settingsKey": "publicityPercent",
      "editable": false
    },
    {
      "title": "DIVIDEND EQUALISATION FUND",
      "settingsKey": "equalisationPercent",
      "editable": false
    },
  ];

  // You must write methods to fetch percentage values for the last four fields:
  Future<List<double>> fetchSettingsPercentages() async {
    return [
      ctrl.settings?.charityFund ?? 0,
      2.0,
      2.5,
      3.0
    ]; // Replace with real fetch
  }

  @override
  void initState() {
    super.initState();
    profitDisSelectedYear = DateTime.now().year;

    columns = [
      PlutoColumn(
        title: 'TITLE',
        field: 'title',
        type: PlutoColumnType.text(),
        readOnly: true,
      ),
      PlutoColumn(
        title: 'PERCENTAGE %',
        field: 'percentage',
        type: PlutoColumnType.number(),
      ),
      PlutoColumn(
        title: 'AMOUNT',
        field: 'amount',
        type: PlutoColumnType.number(),
        readOnly: true,
      ),
    ];

    loadStaticProfitRows();
  }

  Future<void> loadStaticProfitRows() async {
    setState(() => proftDisRowsLoader = true);
    rows.clear();
    final settingsPercentages = await fetchSettingsPercentages();

    int settingsIndex = 0;
    for (int i = 0; i < staticFields.length; i++) {
      final field = staticFields[i];
      double? percent = field["percentage"];
      if (!field["editable"]) {
        percent = settingsPercentages[settingsIndex++];
      }
      rows.add(PlutoRow(cells: {
        'title': PlutoCell(value: field['title']),
        'percentage': PlutoCell(value: percent),
        'amount': PlutoCell(value: field['amount']),
      }));
    }

    setState(() => proftDisRowsLoader = false);
  }

  @override
  Widget build(BuildContext context) {
    return proftDisRowsLoader
        ? const Center(child: CircularProgressIndicator())
        : Padding(
            padding: const EdgeInsets.only(top: 40, left: 15, right: 15),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    DropdownButtonHideUnderline(
                      child: DropdownButtonFormField(
                        value: profitDisSelectedYear,
                        decoration: InputDecoration(
                          hintText: "Select Year",
                          constraints: const BoxConstraints(
                              maxWidth: 150, maxHeight: 45),
                          border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(5)),
                        ),
                        items: yearList.map((year) {
                          return DropdownMenuItem(
                            value: year,
                            child: Text(year.toString()),
                          );
                        }).toList(),
                        onChanged: (value) async {
                          setState(() {
                            profitDisSelectedYear = value;
                          });
                          await loadStaticProfitRows();
                        },
                      ),
                    ),
                    Row(
                      children: [
                        proftDisCSVLoader
                            ? CircularProgressIndicator()
                            : CustomHeaderButton(
                                onPressed: () async => await exportToCSV(),
                                buttonName: 'Export to CSV',
                              ),
                        const SizedBox(width: 5),
                        proftDisPDFLoader
                            ? CircularProgressIndicator()
                            : CustomHeaderButton(
                                onPressed: () async => await exportToPDF(),
                                buttonName: 'Export to PDF',
                              ),
                      ],
                    ),
                  ],
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 22),
                  child: Text(
                    "DISTRIBUTION OF PROFIT FOR THE YEAR $profitDisSelectedYear",
                    style: const TextStyle(
                        fontWeight: FontWeight.bold, fontSize: 16),
                  ),
                ),
                Expanded(
                  child: PlutoGrid(
                    configuration: const PlutoGridConfiguration(
                        columnSize: PlutoGridColumnSizeConfig(
                            autoSizeMode: PlutoAutoSizeMode.equal),
                        scrollbar: PlutoGridScrollbarConfig(
                            draggableScrollbar: false,
                            scrollbarThickness:
                                PlutoScrollbar.defaultThicknessWhileDragging)),
                    columns: columns,
                    rows: rows,
                    onLoaded: (event) {
                      stateManager = event.stateManager;
                    },
                  ),
                ),
              ],
            ),
          );
  }

  Future<void> exportToCSV() async {
    setState(() {
      proftDisCSVLoader = true;
    });
    try {
      final csv = StringBuffer();
      csv.writeln('"TITLE","PERCENTAGE %","AMOUNT"');
      for (var row in rows) {
        final title = row.cells['title']?.value?.toString() ?? '';
        final percentage = row.cells['percentage']?.value?.toString() ?? '';
        final amount = row.cells['amount']?.value?.toString() ?? '';
        csv.writeln('"$title","$percentage","$amount"');
      }
      final bytes = html.Blob([csv.toString()]);
      final url = html.Url.createObjectUrlFromBlob(bytes);
      final anchor = html.AnchorElement(href: url)
        ..setAttribute(
            'download', 'profit_distribution_$profitDisSelectedYear.csv')
        ..click();
      html.Url.revokeObjectUrl(url);
    } catch (e) {}
    setState(() {
      proftDisCSVLoader = false;
    });
  }

  Future<void> exportToPDF() async {
    setState(() {
      proftDisPDFLoader = true;
    });
    try {
      final pdf = pw.Document();
      final headers = ['TITLE', 'PERCENTAGE %', 'AMOUNT'];
      final data = rows
          .map((row) => [
                row.cells['title']?.value?.toString() ?? '',
                row.cells['percentage']?.value?.toString() ?? '',
                row.cells['amount']?.value?.toString() ?? '',
              ])
          .toList();

      pdf.addPage(
        pw.Page(
          pageFormat: PdfPageFormat.a4,
          build: (context) => pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.Text(
                'PROFIT DISTRIBUTION FOR THE YEAR $profitDisSelectedYear',
                style:
                    pw.TextStyle(fontSize: 18, fontWeight: pw.FontWeight.bold),
              ),
              pw.SizedBox(height: 20),
              pw.Table.fromTextArray(
                headers: headers,
                data: data,
                cellAlignment: pw.Alignment.centerLeft,
                headerStyle: pw.TextStyle(fontWeight: pw.FontWeight.bold),
              ),
            ],
          ),
        ),
      );

      final bytes = await pdf.save();
      final blob = html.Blob([bytes], 'application/pdf');
      final url = html.Url.createObjectUrlFromBlob(blob);
      final anchor = html.AnchorElement(href: url)
        ..setAttribute(
            'download', 'profit_distribution_$profitDisSelectedYear.pdf')
        ..click();
      html.Url.revokeObjectUrl(url);
    } catch (e) {}
    setState(() {
      proftDisPDFLoader = false;
    });
  }
}
