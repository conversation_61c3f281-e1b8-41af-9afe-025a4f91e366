import 'package:dropdown_search/dropdown_search.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:foodcorp_admin/controller/homectrl.dart';
import 'package:foodcorp_admin/models/loan_model.dart';
import 'package:foodcorp_admin/models/user_model.dart';
import 'package:foodcorp_admin/shared/router.dart';
import 'package:foodcorp_admin/views/New_Loan_Application/forms/ltloanform.dart';
import 'package:foodcorp_admin/views/New_Loan_Application/forms/stloanform.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import '../../common/page_header.dart';
import '../../shared/methods.dart';

class NewApplicationPage extends StatefulWidget {
  const NewApplicationPage({super.key});

  @override
  State<NewApplicationPage> createState() => _NewApplicationPageState();
}

enum SelectedLoan { longtermloan, shorttermloan }

class _NewApplicationPageState extends State<NewApplicationPage> {
  UserModel? selectedUser;
  SelectedLoan selectedLoan = SelectedLoan.longtermloan;

  @override
  Widget build(BuildContext context) {
    return GetBuilder<HomeCtrl>(builder: (ctrl) {
      List<Loan> filtered = ctrl.newLoan.where((loan) {
        final isNotRejectedLoan =
            loan.rejectionDate == null && loan.rejectionReason == null;
        final isUserMatch =
            selectedUser == null || loan.uid == selectedUser?.docId;
        final isLoanTypeMatch = selectedLoan == SelectedLoan.longtermloan
            ? loan.loanType == 'Long Term Loan'
            : loan.loanType == 'Emergency Loan';
        return isUserMatch && isLoanTypeMatch && isNotRejectedLoan;
      }).toList();

      filtered.sort((a, b) => b.appliedOn.compareTo(a.appliedOn));

      return SingleChildScrollView(
        padding: const EdgeInsets.all(40),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    DropdownSearch<UserModel>(
                      selectedItem: selectedUser,
                      onChanged: (value) {
                        selectedUser = value;
                        if (selectedUser != null) {
                          setState(() {});
                        }
                      },
                      decoratorProps: DropDownDecoratorProps(
                        decoration: InputDecoration(
                          hintText: "Search User",
                          constraints: const BoxConstraints(maxWidth: 450),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(10),
                          ),
                        ),
                      ),
                      popupProps: PopupProps.menu(
                        showSearchBox: true,
                        searchFieldProps: TextFieldProps(
                          decoration: InputDecoration(
                            hintText: ' Search... ',
                            border: UnderlineInputBorder(),
                          ),
                        ),
                        fit: FlexFit.loose,
                      ),
                      itemAsString: (item) => item.name,
                      items: (filter, loadProps) => ctrl.users,
                      compareFn: (item1, item2) =>
                          item1.name.toLowerCase() == item2.name.toLowerCase(),
                    ),
                    if (selectedUser != null) const SizedBox(width: 5),
                    if (selectedUser != null)
                      IconButton(
                        onPressed: () {
                          setState(() {
                            selectedUser = null;
                          });
                        },
                        icon: const Icon(Icons.clear, size: 30),
                      ),
                  ],
                ),
                CustomHeaderButton(
                  onPressed: () {
                    context.go(Routes.rejectedLoan);
                  },
                  buttonName: "Rejected Applications",
                )
              ],
            ),
            const SizedBox(height: 20),
            CupertinoSlidingSegmentedControl<SelectedLoan>(
              thumbColor: Colors.green.shade300,
              groupValue: selectedLoan,
              children: {
                SelectedLoan.longtermloan: Text(
                  "Long Term Loan",
                  style: TextStyle(
                    color: selectedLoan == SelectedLoan.longtermloan
                        ? Colors.white
                        : Colors.black,
                  ),
                ),
                SelectedLoan.shorttermloan: Text(
                  "Emergency Loan",
                  style: TextStyle(
                    color: selectedLoan == SelectedLoan.shorttermloan
                        ? Colors.white
                        : Colors.black,
                  ),
                ),
              },
              onValueChanged: (SelectedLoan? value) {
                if (value != null) {
                  setState(() {
                    selectedLoan = value;
                  });
                }
              },
            ),
            const SizedBox(height: 30),
            const Row(
              children: [
                HeaderTxt(txt: 'Sr.no'),
                HeaderTxt(txt: "Applied On"),
                HeaderTxt(txt: "Name"),
                HeaderTxt(txt: "DO Office"),
                HeaderTxt(txt: 'Loan Amount'),
                // HeaderTxt(txt: 'Appl No.'),
                // HeaderTxt(txt: 'Loan Type'),
              ],
            ),
            const SizedBox(height: 30),
            if (filtered.isEmpty)
              const Padding(
                padding: EdgeInsets.all(10.0),
                child: Center(
                  child: Text("No applications found.",
                      style: TextStyle(fontSize: 16)),
                ),
              ),
            ...List.generate(filtered.length, (index) {
              final user = ctrl.users.firstWhereOrNull(
                  (element) => element.docId == filtered[index].uid);
              final doOffice = ctrl.districtoffice.firstWhereOrNull(
                  (element) => element.docId == user?.districtoffice);

              return InkWell(
                onTap: () => applDetailsDialog(context, ctrl, filtered[index]),
                child: Container(
                  height: 40,
                  color: index % 2 == 0 ? Colors.white : null,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 10.0),
                    child: Row(
                      children: [
                        Expanded(child: Text("${index + 1}")),
                        Expanded(
                          child: Text(DateFormat('dd-MM-yyyy')
                              .format(filtered[index].appliedOn)),
                        ),
                        Expanded(child: Text(user?.name ?? "")),
                        Expanded(child: Text(doOffice?.name ?? "-")),
                        Expanded(
                            child: Text(
                                filtered[index].appliedLoanAmt.toString())),
                        // Expanded(
                        //     child:
                        //         Text(filtered[index].applicationNo.toString())),
                        // Expanded(child: Text(filtered[index].loanType)),
                      ],
                    ),
                  ),
                ),
              );
            }),
          ],
        ),
      );
    });
  }

  Future<dynamic> applDetailsDialog(
      BuildContext context, HomeCtrl ctrl, Loan loan) {
    return showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.white,
        scrollable: true,
        content: SingleChildScrollView(
          child: ConstrainedBox(
            constraints: const BoxConstraints(maxHeight: 400),
            child: SizedBox(
              width: 800,
              child: loan.loanType == 'Long Term Loan'
                  ? Ltloanform(loan: loan, index: ctrl.newLoan.indexOf(loan))
                  : Stloanform(loan: loan, index: ctrl.newLoan.indexOf(loan)),
            ),
          ),
        ),
        title: Center(child: const Text("Application Details")),
      ),
    );
  }
}
