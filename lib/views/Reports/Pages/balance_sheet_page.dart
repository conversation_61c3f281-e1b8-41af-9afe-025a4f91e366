import 'package:flutter/material.dart';
import 'package:foodcorp_admin/common/page_header.dart';
import 'package:foodcorp_admin/controller/homectrl.dart';
import 'package:foodcorp_admin/models/profit_distribution_model.dart';
import 'package:foodcorp_admin/shared/firebase.dart';
import 'package:foodcorp_admin/shared/methods.dart';
import 'package:foodcorp_admin/shared/const.dart';
import 'package:get/get.dart';
import 'package:pdf/pdf.dart';
import 'package:pluto_grid/pluto_grid.dart';
import 'dart:html' as html; // For web file download (if Flutter Web)
import 'package:pdf/widgets.dart' as pw;

class BalanceSheetPage extends StatefulWidget {
  const BalanceSheetPage({super.key});

  @override
  State<BalanceSheetPage> createState() => _BalanceSheetPageState();
}

class _BalanceSheetPageState extends State<BalanceSheetPage> {
  late PlutoGridStateManager stateManager;

  int? yearlyBsSelectedYear;

  List<int> yearList = TheFinancialYear.generateFinancialYearStartYearsList();

  List<PlutoRow> rows = [];

  int startingYear = 2024;

  final List<PlutoColumn> columns = [
    PlutoColumn(
      title:
          '${DateTime.now().year - 1}-${(DateTime.now().year).toString().substring(2)}',
      field: 'year',
      type: PlutoColumnType.number(),
      enableEditingMode: false,
      backgroundColor: Color(0xffC9E9D2),
    ),
    PlutoColumn(
      title: 'SHARE CAPITAL & LIABILITY',
      field: 'particulars',
      type: PlutoColumnType.text(),
      enableEditingMode: false,
      backgroundColor: Color(0xffC9E9D2),
    ),
    PlutoColumn(
      title:
          '${DateTime.now().year}-${(DateTime.now().year + 1).toString().substring(2)}',
      field: 'year2',
      type: PlutoColumnType.number(),
      enableEditingMode: false,
      backgroundColor: Color(0xffC9E9D2),
    ),
    PlutoColumn(
      title:
          '${DateTime.now().year - 1}-${(DateTime.now().year).toString().substring(2)}',
      field: 'year3',
      type: PlutoColumnType.number(),
      enableEditingMode: false,
      backgroundColor: Color(0xffC9E9D2),
    ),
    PlutoColumn(
      title: 'ASSETS AND LOAN',
      field: 'assets_and_loan',
      type: PlutoColumnType.text(),
      enableEditingMode: false,
      backgroundColor: Color(0xffC9E9D2),
    ),
    PlutoColumn(
      title:
          '${DateTime.now().year}-${(DateTime.now().year + 1).toString().substring(2)}',
      field: 'year4',
      type: PlutoColumnType.number(),
      enableEditingMode: false,
      backgroundColor: Color(0xffC9E9D2),
    ),
  ];

  @override
  void initState() {
    super.initState();
    yearlyBsSelectedYear = DateTime.now().year;
    addBsRows(year: yearlyBsSelectedYear);
  }

  Future<void> addBsRows({int? year}) async {
    final snap = await FBFireStore.profitDistribution
        .where('year', isEqualTo: yearlyBsSelectedYear)
        .get();

    if (snap.docs.isEmpty) {
      if (mounted) {
        showCtcAppSnackBar(
            context, 'No data for the selected year $yearlyBsSelectedYear');
      }

      setState(() {
        rows = [];
        stateManager.removeAllRows();
      });

      return;
    }

    List<ProfitDistributionModel> pdModel =
        snap.docs.map((doc) => ProfitDistributionModel.fromSnap(doc)).toList();

    List<PlutoRow> newRows = pdModel.map((model) {
      return PlutoRow(
        cells: {
          'year': PlutoCell(value: 0),
          'particulars':
              PlutoCell(value: '${model.title} (${model.percentage}%)'),
          'year2': PlutoCell(value: model.amount),
          'year3': PlutoCell(value: 0),
          'assets_and_loan': PlutoCell(value: 0),
          'year4': PlutoCell(value: 0),
        },
      );
    }).toList();

    setState(() {
      rows = newRows;
      stateManager.removeAllRows();
      stateManager.appendRows(rows);
    });
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<HomeCtrl>(builder: (ctrl) {
      return Padding(
        padding: const EdgeInsets.only(top: 40, left: 15, right: 15),
        child: Column(children: [
          Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
            DropdownButtonHideUnderline(
                child: DropdownButtonFormField(
                    focusColor: Colors.transparent,
                    dropdownColor: Colors.white,
                    value: yearlyBsSelectedYear,
                    decoration: InputDecoration(
                        hintText: "Select Year",
                        constraints:
                            const BoxConstraints(maxWidth: 150, maxHeight: 45),
                        border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(5))),
                    items: List.generate(
                      yearList.length,
                      (index) {
                        return DropdownMenuItem(
                          value: yearList[index],
                          child: Text(yearList[index].toString()),
                        );
                      },
                    ),
                    onChanged: (value) async {
                      if (value == null) return;

                      setState(() {
                        yearlyBsSelectedYear = value;
                      });

                      await addBsRows(year: yearlyBsSelectedYear);
                    })),
            Row(
                crossAxisAlignment: CrossAxisAlignment.end,
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  CustomHeaderButton(
                    onPressed: () async {
                      await exportToCSV();
                    },
                    buttonName: 'Export to CSV',
                    // child: Text(''),
                  ),
                  SizedBox(width: 5),
                  CustomHeaderButton(
                    onPressed: () async {
                      await exportToPDF();
                    },
                    buttonName: 'Export to PDF',
                    // child: Text(''),
                  ),
                  // SizedBox(width: 5),
                  // slIsLoading
                  //     ? CircularProgressIndicator()
                  // //     :
                  // CustomHeaderButton(
                  //     onPressed: () async {
                  //       // await slOnSave(ctrl);
                  //     },
                  //     buttonName: 'SAVE')
                ])
          ]),
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 22),
            child: Text("BALANCE SHEET FOR THE YEAR $yearlyBsSelectedYear",
                style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16)),
          ),
          Expanded(
              child: Container(
                  decoration: BoxDecoration(
                      border: Border.all(color: Colors.transparent)),
                  // height: size.height, //  - 30,
                  child: PlutoGrid(
                    columns: columns,
                    rows: rows,
                    onLoaded: (PlutoGridOnLoadedEvent event) {
                      stateManager = event.stateManager;
                      stateManager.setShowColumnFilter(true);
                      stateManager.notifyListeners();
                      stateManager.notifyListenersOnPostFrame();
                    },
                    onChanged: (PlutoGridOnChangedEvent event) {},
                    configuration: const PlutoGridConfiguration(
                        columnSize: PlutoGridColumnSizeConfig(
                            autoSizeMode: PlutoAutoSizeMode.equal),
                        scrollbar: PlutoGridScrollbarConfig(
                            draggableScrollbar: true,
                            isAlwaysShown: true,
                            scrollbarThickness:
                                PlutoScrollbar.defaultThicknessWhileDragging)),
                  )))
        ]),
      );
    });
  }

// CSV EXPORT FUNCTION
  Future<void> exportToCSV() async {
    if (stateManager.rows.isEmpty) {
      showCtcAppSnackBar(context, "No data to export");
      return;
    }

    final csv = StringBuffer();

    // Define headers according to Balance Sheet columns
    final headers = [
      '${DateTime.now().year - 1}-${(DateTime.now().year).toString().substring(2)}',
      'SHARE CAPITAL & LIABILITY',
      '${DateTime.now().year}-${(DateTime.now().year + 1).toString().substring(2)}',
      '${DateTime.now().year - 1}-${(DateTime.now().year).toString().substring(2)}',
      'ASSETS AND LOAN',
      '${DateTime.now().year}-${(DateTime.now().year + 1).toString().substring(2)}',
    ];

    // Write CSV headers
    csv.writeln(headers.map((h) => '"$h"').join(','));

    for (var row in stateManager.rows) {
      final rowData = [
        row.cells['year']?.value?.toString() ?? '',
        row.cells['particulars']?.value?.toString() ?? '',
        row.cells['year2']?.value?.toString() ?? '',
        row.cells['year3']?.value?.toString() ?? '',
        row.cells['assets_and_loan']?.value?.toString() ?? '',
        row.cells['year4']?.value?.toString() ?? '',
      ];

      final quotedRow =
          rowData.map((field) => '"${field.replaceAll('"', '""')}"');
      csv.writeln(quotedRow.join(','));
    }

    final csvBytes = html.Blob([csv.toString()], 'text/csv');
    final csvUrl = html.Url.createObjectUrlFromBlob(csvBytes);

    final anchor = html.AnchorElement(href: csvUrl)
      ..setAttribute('download',
          'balance_sheet_${yearlyBsSelectedYear ?? TheFinancialYear.getCurrentYearForDatabase()}.csv')
      ..click();

    html.Url.revokeObjectUrl(csvUrl);
  }

// PDF EXPORT FUNCTION (needs package:pdf and package:printing)
  Future<void> exportToPDF() async {
    if (stateManager.rows.isEmpty) {
      showCtcAppSnackBar(context, "No data to export");
      return;
    }

    final pdf = pw.Document();

    final headers = [
      '${DateTime.now().year - 1}-${(DateTime.now().year).toString().substring(2)}',
      'SHARE CAPITAL & LIABILITY',
      '${DateTime.now().year}-${(DateTime.now().year + 1).toString().substring(2)}',
      '${DateTime.now().year - 1}-${(DateTime.now().year).toString().substring(2)}',
      'ASSETS AND LOAN',
      '${DateTime.now().year}-${(DateTime.now().year + 1).toString().substring(2)}',
    ];

    final data = stateManager.rows
        .map((row) => [
              row.cells['year']?.value?.toString() ?? '',
              row.cells['particulars']?.value?.toString() ?? '',
              row.cells['year2']?.value?.toString() ?? '',
              row.cells['year3']?.value?.toString() ?? '',
              row.cells['assets_and_loan']?.value?.toString() ?? '',
              row.cells['year4']?.value?.toString() ?? '',
            ])
        .toList();

    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a3.landscape,
        margin: pw.EdgeInsets.all(10),
        build: (context) => [
          pw.Text(
            'BALANCE SHEET FOR THE YEAR ${yearlyBsSelectedYear ?? DateTime.now().year}',
            style: pw.TextStyle(fontSize: 14, fontWeight: pw.FontWeight.bold),
          ),
          pw.SizedBox(height: 10),
          pw.Table.fromTextArray(
            headers: headers,
            data: data,
            cellAlignment: pw.Alignment.centerLeft,
            headerStyle: pw.TextStyle(fontWeight: pw.FontWeight.bold),
            border: pw.TableBorder.all(width: 0.5),
            cellPadding: const pw.EdgeInsets.all(5),
          ),
        ],
      ),
    );

    final bytes = await pdf.save();

    final blob = html.Blob([bytes], 'application/pdf');
    final url = html.Url.createObjectUrlFromBlob(blob);
    final anchor = html.AnchorElement(href: url)
      ..setAttribute('download',
          'balance_sheet_${yearlyBsSelectedYear ?? TheFinancialYear.getCurrentYearForDatabase()}.pdf')
      ..click();
    html.Url.revokeObjectUrl(url);
  }
}
