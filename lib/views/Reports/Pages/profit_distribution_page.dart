import 'package:flutter/material.dart';
import 'package:foodcorp_admin/common/page_header.dart';
import 'package:foodcorp_admin/controller/homectrl.dart';
import 'package:foodcorp_admin/shared/firebase.dart';
import 'package:foodcorp_admin/shared/const.dart';
import 'package:get/get.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:pdf/pdf.dart';
import 'dart:html' as html;
import 'package:pluto_grid/pluto_grid.dart';

class ProfitDistributionPage extends StatefulWidget {
  const ProfitDistributionPage({super.key});

  @override
  State<ProfitDistributionPage> createState() => _ProfitDistributionPageState();
}

class _ProfitDistributionPageState extends State<ProfitDistributionPage> {
  late PlutoGridStateManager stateManager;

  bool proftDisRowsLoader = false;
  bool proftDisCSVLoader = false;
  bool proftDisPDFLoader = false;
  bool proftDisSaveLoader = false;
  String? profitDisSelectedFinancialYear;
  int? profitDisSelectedYear;

  List<String> financialYearList =
      TheFinancialYear.generateFinancialYearsList();
  List<int> yearList = TheFinancialYear.generateFinancialYearStartYearsList();

  final hctrl = Get.find<HomeCtrl>();

  late List<PlutoColumn> columns;
  List<PlutoRow> rows = [];

  final List<Map<String, dynamic>> staticFields = [
    {"title": "DIVIDEND", "settingsKey": null, "editable": false},
    {"title": "RESERVE FUND", "settingsKey": null, "editable": true},
    {
      "title": "RESERVE FOR GOLDEN JUBILEE",
      "settingsKey": null,
      "editable": true
    },
    {"title": "EDUCATION FUND", "settingsKey": null, "editable": true},
    {"title": "BENEVULENT FUND", "settingsKey": null, "editable": true},
    // Last 4 fields, % should come from settings and not be editable:
    {
      "title": "GENERAL RESERVE FUND",
      "settingsKey": "generalReservedFund",
      "editable": false
    },
    {"title": "CHARITY FUND", "settingsKey": "charityFund", "editable": false},
    {
      "title": "CO-OP PUBLICITY FUND",
      "settingsKey": "coopPublicityFund",
      "editable": false
    },
    {
      "title": "DIVIDEND EQUALISATION FUND",
      "settingsKey": "dividentEquivalisationFund",
      "editable": false
    },
  ];

  Future<double> calculateDividendAmount() async {
    double totalProgressiveShares = 0.0;
    final int selectedYear = profitDisSelectedYear ?? DateTime.now().year;

    for (var user in hctrl.users) {
      num userProgressiveTotal = 0;

      final userMonthlyData = hctrl.usermonthly.where(
          (e) => e.cpfNo == user.cpfNo && e.selectedyear == selectedYear);

      for (var monthData in userMonthlyData) {
        userProgressiveTotal += (monthData.shareValue ?? 0);
      }

      totalProgressiveShares += userProgressiveTotal;
    }

    final dividendRate = num.tryParse(hctrl.settings?.dividentRate ?? '0') ?? 0;

    return (totalProgressiveShares * dividendRate) / 1200;
  }

  Future<double> fetchProfitAmount() async {
    try {
      final pnlDataSnapshot = await FBFireStore.profitandloss
          .where("year", isEqualTo: profitDisSelectedYear)
          .get();

      if (pnlDataSnapshot.docs.isNotEmpty) {
        final docRef = pnlDataSnapshot.docs.first.reference;

        // Get the "PROFIT AND DIVIDENT" entry from pnlEntries subcollection
        final pnlEntriesSnapshot = await docRef
            .collection("pnlEntries")
            .where("title", isEqualTo: "PROFIT AND DIVIDENT")
            .get();

        if (pnlEntriesSnapshot.docs.isNotEmpty) {
          final profitAndDividendAmount =
              pnlEntriesSnapshot.docs.first.get('amount') ?? 0;
          final dividendAmount = await calculateDividendAmount();

          // Return pure profit (profit and dividend - dividend)
          return (profitAndDividendAmount - dividendAmount).toDouble();
        }
      }
    } catch (e) {
      debugPrint("Error fetching profit amount: $e");
    }
    return 0.0;
  }

  Future<void> recalculateAmounts() async {
    final dividendAmount = await calculateDividendAmount();
    final profitAmount = await fetchProfitAmount();
    final totalDistributableAmount = profitAmount + dividendAmount;

    for (int i = 0; i < stateManager.rows.length; i++) {
      final row = stateManager.rows[i];
      final field = staticFields[i];
      final percentage = row.cells['percentage']?.value ?? 0;

      double amount;
      if (field['title'] == 'DIVIDEND') {
        amount = dividendAmount;
      } else {
        amount = totalDistributableAmount > 0
            ? (percentage / 100) * totalDistributableAmount
            : 0;
      }

      stateManager.changeCellValue(
        row.cells['amount']!,
        amount,
        notify: false,
      );
    }
  }

  @override
  void initState() {
    super.initState();
    financialYearList = TheFinancialYear.generateFinancialYearsList();
    yearList = TheFinancialYear.generateFinancialYearStartYearsList();
    profitDisSelectedFinancialYear = TheFinancialYear.getCurrentFinancialYear();
    profitDisSelectedYear = TheFinancialYear.getCurrentFinancialYearStartYear();

    columns = [
      PlutoColumn(
        title: 'TITLE',
        field: 'title',
        type: PlutoColumnType.text(),
        readOnly: true,
      ),
      PlutoColumn(
        title: 'PERCENTAGE %',
        field: 'percentage',
        type: PlutoColumnType.number(),
        readOnly: false, // Will be controlled per row
      ),
      PlutoColumn(
        title: 'AMOUNT',
        field: 'amount',
        type: PlutoColumnType.number(),
        readOnly: true,
      ),
    ];

    loadStaticProfitRows();
  }

  Future<void> loadStaticProfitRows() async {
    setState(() => proftDisRowsLoader = true);
    rows.clear();

    // Get dividend amount and profit amount
    final dividendAmount = await calculateDividendAmount();
    final profitAmount = await fetchProfitAmount();
    final totalDistributableAmount = profitAmount + dividendAmount;

    for (int i = 0; i < staticFields.length; i++) {
      final field = staticFields[i];
      double? percent;
      double? amount;

      if (field['title'] == 'DIVIDEND') {
        // Dividend is calculated directly, not from percentage
        percent = totalDistributableAmount > 0
            ? (dividendAmount / totalDistributableAmount) * 100
            : 0;
        amount = dividendAmount;
      } else if (!field["editable"] && field["settingsKey"] != null) {
        // Fetch percentage from settings for non-editable fields
        final settingsKey = field["settingsKey"];
        String? settingsValue;

        switch (settingsKey) {
          case "generalReservedFund":
            settingsValue = hctrl.settings?.generalReservedFund;
            break;
          case "charityFund":
            settingsValue = hctrl.settings?.charityFund;
            break;
          case "coopPublicityFund":
            settingsValue = hctrl.settings?.coopPublicityFund;
            break;
          case "dividentEquivalisationFund":
            settingsValue = hctrl.settings?.dividentEquivalisationFund;
            break;
        }

        percent = double.tryParse(settingsValue ?? '0') ?? 0;
        amount = totalDistributableAmount > 0
            ? (percent / 100) * totalDistributableAmount
            : 0;
      } else {
        // Editable fields start with 0
        percent = 0;
        amount = 0;
      }

      rows.add(PlutoRow(cells: {
        'title': PlutoCell(value: field['title']),
        'percentage': PlutoCell(value: percent),
        'amount': PlutoCell(value: amount),
      }));
    }

    setState(() => proftDisRowsLoader = false);
  }

  // Load existing saved data for the selected year
  Future<void> loadExistingData() async {
    try {
      final existingSnapshot = await FBFireStore.profitDistribution
          .where("year", isEqualTo: profitDisSelectedYear)
          .get();

      if (existingSnapshot.docs.isNotEmpty) {
        final doc = existingSnapshot.docs.first;
        final entries = doc.get('entries') as List<dynamic>? ?? [];

        // Update the grid with saved data
        for (int i = 0;
            i < entries.length && i < stateManager.rows.length;
            i++) {
          final entry = entries[i];
          final row = stateManager.rows[i];

          // Only update editable fields from saved data
          if (entry['editable'] == true) {
            stateManager.changeCellValue(
              row.cells['percentage']!,
              entry['percentage'] ?? 0,
              notify: false,
            );
          }

          stateManager.changeCellValue(
            row.cells['amount']!,
            entry['amount'] ?? 0,
            notify: false,
          );
        }
      }
    } catch (e) {
      debugPrint("Error loading existing data: $e");
    }
  }

  @override
  Widget build(BuildContext context) {
    return proftDisRowsLoader
        ? const Center(child: CircularProgressIndicator())
        : Padding(
            padding: const EdgeInsets.only(top: 40, left: 15, right: 15),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    DropdownButtonHideUnderline(
                      child: DropdownButtonFormField(
                        focusColor: Colors.transparent,
                        dropdownColor: Colors.white,
                        value: profitDisSelectedYear,
                        decoration: InputDecoration(
                          hintText: "Select Year",
                          constraints: const BoxConstraints(
                              maxWidth: 150, maxHeight: 45),
                          border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(5)),
                        ),
                        items: List.generate(
                          yearList.length,
                          (index) => DropdownMenuItem(
                            value: yearList[index],
                            child: Text(yearList[index].toString()),
                          ),
                        ),
                        onChanged: (value) async {
                          setState(() {
                            profitDisSelectedYear = value;
                            profitDisSelectedFinancialYear =
                                TheFinancialYear.startYearToFinancialYear(
                                    value!);
                          });
                          await loadStaticProfitRows();
                          await loadExistingData();
                        },
                      ),
                    ),
                    Row(
                      children: [
                        proftDisCSVLoader
                            ? CircularProgressIndicator()
                            : CustomHeaderButton(
                                onPressed: () async => await exportToCSV(),
                                buttonName: 'Export to CSV',
                              ),
                        const SizedBox(width: 5),
                        proftDisPDFLoader
                            ? CircularProgressIndicator()
                            : CustomHeaderButton(
                                onPressed: () async => await exportToPDF(),
                                buttonName: 'Export to PDF',
                              ),
                        const SizedBox(width: 5),
                        proftDisSaveLoader
                            ? CircularProgressIndicator()
                            : CustomHeaderButton(
                                onPressed: () async =>
                                    await saveProfitDistributionData(),
                                buttonName: 'SAVE',
                              ),
                      ],
                    ),
                  ],
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 22),
                  child: Text(
                    "DISTRIBUTION OF PROFIT FOR THE YEAR $profitDisSelectedFinancialYear",
                    style: const TextStyle(
                        fontWeight: FontWeight.bold, fontSize: 16),
                  ),
                ),
                Expanded(
                  child: PlutoGrid(
                    configuration: const PlutoGridConfiguration(
                        columnSize: PlutoGridColumnSizeConfig(
                            autoSizeMode: PlutoAutoSizeMode.equal),
                        scrollbar: PlutoGridScrollbarConfig(
                            draggableScrollbar: false,
                            scrollbarThickness:
                                PlutoScrollbar.defaultThicknessWhileDragging)),
                    columns: columns,
                    rows: rows,
                    onLoaded: (event) async {
                      stateManager = event.stateManager;
                      await loadExistingData();
                    },
                    onChanged: (PlutoGridOnChangedEvent event) {
                      // Handle percentage changes for editable fields
                      if (event.column.field == 'percentage') {
                        final rowIndex = event.rowIdx;
                        final field = staticFields[rowIndex];

                        if (field['editable'] == true) {
                          // Recalculate amount based on percentage
                          recalculateAmounts();
                        }
                      }
                    },
                  ),
                ),
              ],
            ),
          );
  }

  Future<void> saveProfitDistributionData() async {
    setState(() {
      proftDisSaveLoader = true;
    });

    try {
      // Check if document already exists for this year
      final existingSnapshot = await FBFireStore.profitDistribution
          .where("year", isEqualTo: profitDisSelectedYear)
          .get();

      Map<String, dynamic> profitDistributionData = {
        'year': profitDisSelectedYear,
        'financialYear': profitDisSelectedFinancialYear,
        'createdAt': Timestamp.now(),
        'updatedAt': Timestamp.now(),
        'entries': [],
      };

      // Collect all the distribution entries
      for (int i = 0; i < stateManager.rows.length; i++) {
        final row = stateManager.rows[i];
        final field = staticFields[i];

        final title = row.cells['title']?.value?.toString() ?? '';
        final percentage = row.cells['percentage']?.value ?? 0;
        final amount = row.cells['amount']?.value ?? 0;

        profitDistributionData['entries'].add({
          'title': title,
          'percentage': percentage,
          'amount': amount,
          'editable': field['editable'],
          'settingsKey': field['settingsKey'],
        });
      }

      if (existingSnapshot.docs.isNotEmpty) {
        // Update existing document
        final docRef = existingSnapshot.docs.first.reference;
        profitDistributionData['updatedAt'] = Timestamp.now();
        profitDistributionData.remove('createdAt'); // Don't update createdAt

        await docRef.update(profitDistributionData);

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Profit Distribution data updated successfully!'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        // Create new document
        await FBFireStore.profitDistribution.add(profitDistributionData);

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Profit Distribution data saved successfully!'),
              backgroundColor: Colors.green,
            ),
          );
        }
      }
    } catch (e) {
      debugPrint("Error saving profit distribution data: $e");
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error saving data: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        proftDisSaveLoader = false;
      });
    }
  }

  Future<void> exportToCSV() async {
    setState(() {
      proftDisCSVLoader = true;
    });
    try {
      final csv = StringBuffer();
      csv.writeln('"TITLE","PERCENTAGE %","AMOUNT"');
      for (var row in rows) {
        final title = row.cells['title']?.value?.toString() ?? '';
        final percentage = row.cells['percentage']?.value?.toString() ?? '';
        final amount = row.cells['amount']?.value?.toString() ?? '';
        csv.writeln('"$title","$percentage","$amount"');
      }
      final bytes = html.Blob([csv.toString()]);
      final url = html.Url.createObjectUrlFromBlob(bytes);
      final anchor = html.AnchorElement(href: url)
        ..setAttribute(
            'download', 'profit_distribution_$profitDisSelectedYear.csv')
        ..click();
      html.Url.revokeObjectUrl(url);
    } catch (e) {}
    setState(() {
      proftDisCSVLoader = false;
    });
  }

  Future<void> exportToPDF() async {
    setState(() {
      proftDisPDFLoader = true;
    });
    try {
      final pdf = pw.Document();
      final headers = ['TITLE', 'PERCENTAGE %', 'AMOUNT'];
      final data = rows
          .map((row) => [
                row.cells['title']?.value?.toString() ?? '',
                row.cells['percentage']?.value?.toString() ?? '',
                row.cells['amount']?.value?.toString() ?? '',
              ])
          .toList();

      pdf.addPage(
        pw.Page(
          pageFormat: PdfPageFormat.a4,
          build: (context) => pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.Text(
                'PROFIT DISTRIBUTION FOR THE YEAR $profitDisSelectedFinancialYear',
                style:
                    pw.TextStyle(fontSize: 18, fontWeight: pw.FontWeight.bold),
              ),
              pw.SizedBox(height: 20),
              pw.Table.fromTextArray(
                headers: headers,
                data: data,
                cellAlignment: pw.Alignment.centerLeft,
                headerStyle: pw.TextStyle(fontWeight: pw.FontWeight.bold),
              ),
            ],
          ),
        ),
      );

      final bytes = await pdf.save();
      final blob = html.Blob([bytes], 'application/pdf');
      final url = html.Url.createObjectUrlFromBlob(blob);
      final anchor = html.AnchorElement(href: url)
        ..setAttribute(
            'download', 'profit_distribution_$profitDisSelectedYear.pdf')
        ..click();
      html.Url.revokeObjectUrl(url);
    } catch (e) {}
    setState(() {
      proftDisPDFLoader = false;
    });
  }
}
