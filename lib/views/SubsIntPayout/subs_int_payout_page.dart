// ignore_for_file: use_build_context_synchronously
import 'package:flutter/material.dart';
import 'package:foodcorp_admin/common/page_header.dart';
import 'package:foodcorp_admin/controller/homectrl.dart';
import 'package:foodcorp_admin/models/user_model.dart';
import 'package:foodcorp_admin/shared/firebase.dart';
import 'package:foodcorp_admin/shared/methods.dart';
import 'package:foodcorp_admin/shared/const.dart';
import 'package:get/get.dart';
import 'package:pluto_grid/pluto_grid.dart';
import 'package:csv/csv.dart';
import 'package:file_saver/file_saver.dart';
import 'dart:typed_data';

class SubsIntPayoutPage extends StatefulWidget {
  const SubsIntPayoutPage({super.key});

  @override
  State<SubsIntPayoutPage> createState() => _SubsIntPayoutPageState();
}

class _SubsIntPayoutPageState extends State<SubsIntPayoutPage> {
  late PlutoGridStateManager stateManager;

  String? subsIntSelectedYear;

  bool subsIntIsLoading = false;
  bool expSubsIntIsLoading = false;

  bool hasShownEmptySnackBar = false;

  List<String> yearList = TheFinancialYear.generateFinancialYearsList();

  List<PlutoRow> rows = [];

  @override
  void initState() {
    super.initState();
    yearList = TheFinancialYear.generateFinancialYearsList();
    subsIntSelectedYear = TheFinancialYear.getCurrentFinancialYear();
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<HomeCtrl>(builder: (ctrl) {
      if (ctrl.settings?.subscriptionInterest == null) {
        return const Center(child: CircularProgressIndicator());
      }

      WidgetsBinding.instance.addPostFrameCallback((_) {
        subsIntInsertRows(ctrl.users, ctrl);
      });

      final List<PlutoColumn> columns = [
        PlutoColumn(
          title: 'SR. NO',
          field: 'sr_no',
          type: PlutoColumnType.text(),
          enableEditingMode: false,
          backgroundColor: Color(0xffC9E9D2),
        ),
        PlutoColumn(
          title: 'CPF NO.',
          field: 'cpf_no',
          type: PlutoColumnType.text(),
          enableEditingMode: false,
          backgroundColor: Color(0xffC9E9D2),
        ),
        PlutoColumn(
          title: 'NAME OF MEMBER',
          field: 'name',
          type: PlutoColumnType.text(),
          enableEditingMode: false,
          backgroundColor: Color(0xffC9E9D2),
        ),
        PlutoColumn(
          title: 'DISTRICT OFFICE',
          field: 'district_office',
          type: PlutoColumnType.text(),
          enableEditingMode: false,
          backgroundColor: Color(0xffC9E9D2),
        ),
        PlutoColumn(
          title: 'SUBSCRIPTION AMOUNT',
          field: 'subscription_amount',
          type: PlutoColumnType.text(),
          enableEditingMode: true,
        ),
        PlutoColumn(
          title:
              'INTEREST AMOUNT (${ctrl.settings?.subscriptionInterest ?? 0}%)',
          field: 'interest_amount',
          type: PlutoColumnType.text(),
          enableEditingMode: true,
        ),
        PlutoColumn(
          title: 'Status',
          field: 'status',
          type: PlutoColumnType.text(),
          enableEditingMode: true,
        ),
      ];

      return Padding(
        padding: const EdgeInsets.only(top: 40, left: 15, right: 15),
        child: Column(
          children: [
            Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
              DropdownButtonHideUnderline(
                  child: DropdownButtonFormField(
                      focusColor: Colors.transparent,
                      dropdownColor: Colors.white,
                      value: subsIntSelectedYear,
                      decoration: InputDecoration(
                          hintText: "Select Year",
                          constraints: const BoxConstraints(
                              maxWidth: 150, maxHeight: 45),
                          border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(5))),
                      items: List.generate(
                        yearList.length,
                        (index) {
                          return DropdownMenuItem(
                            value: yearList[index],
                            child: Text(yearList[index].toString()),
                          );
                        },
                      ),
                      onChanged: (value) async {
                        setState(() {
                          subsIntSelectedYear = value;
                          hasShownEmptySnackBar = false;
                        });
                        stateManager.removeAllRows();
                        subsIntInsertRows(ctrl.users, ctrl);
                      })),
              Text("Total Members: ${ctrl.users.length}"),
              Text(
                  "Total Subscription: ${ctrl.users.isNotEmpty ? ctrl.users.map((e) => e.totalSubs ?? 0).reduce((a, b) => a + b) : 0}"),
              Text(
                  "Total Interest: ${ctrl.users.isNotEmpty ? ctrl.users.map((e) => (e.totalSubs ?? 0) * (num.tryParse(ctrl.settings?.subscriptionInterest ?? '0') ?? 0) / 100).reduce((a, b) => a + b) : 0}"),
              Row(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    expSubsIntIsLoading
                        ? CircularProgressIndicator()
                        : CustomHeaderButton(
                            onPressed: () async {
                              await exportToCSV();
                            },
                            buttonName: 'Export to CSV'),
                    SizedBox(width: 5),
                    subsIntIsLoading
                        ? CircularProgressIndicator()
                        : CustomHeaderButton(
                            onPressed: () async {
                              if (rows.every((row) =>
                                  row.cells['status']?.value == 'Paid')) {
                                showCtcAppSnackBar(context,
                                    "Payout Already Done for this year");
                                return;
                              }
                              bool isLoading = false;
                              await showDialog(
                                context: context,
                                barrierDismissible: false,
                                builder: (context) {
                                  return StatefulBuilder(
                                    builder: (context, setState) {
                                      return AlertDialog(
                                        title: Text('Confirm Payout'),
                                        content: Text(
                                            'Are you sure you want to proceed with the payout?'),
                                        actions: [
                                          TextButton(
                                            onPressed: isLoading
                                                ? null
                                                : () =>
                                                    Navigator.of(context).pop(),
                                            child: Text('No'),
                                          ),
                                          TextButton(
                                            onPressed: isLoading
                                                ? null
                                                : () async {
                                                    setState(
                                                        () => isLoading = true);
                                                    subsIntPayoutOnPressed(
                                                        ctrl);
                                                    setState(() =>
                                                        isLoading = false);
                                                    Navigator.of(context).pop();
                                                  },
                                            child: isLoading
                                                ? CircularProgressIndicator()
                                                : Text('Yes'),
                                          ),
                                        ],
                                      );
                                    },
                                  );
                                },
                              );
                            },
                            buttonName: 'PAY OUT')
                  ])
            ]),
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 22),
              child: Text(
                  "SUBSCRIPTION INEREST PAYOUT FOR THE YEAR $subsIntSelectedYear",
                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16)),
            ),
            Expanded(
              child: Container(
                decoration: BoxDecoration(
                    border: Border.all(color: Colors.transparent)),
                child: PlutoGrid(
                  columns: columns,
                  rows: rows,
                  onLoaded: (PlutoGridOnLoadedEvent event) {
                    stateManager = event.stateManager;
                    stateManager.setShowColumnFilter(true);
                    stateManager.notifyListeners();
                    stateManager.notifyListenersOnPostFrame();
                  },
                  onChanged: (PlutoGridOnChangedEvent event) {},
                  configuration: const PlutoGridConfiguration(
                      columnSize: PlutoGridColumnSizeConfig(
                          autoSizeMode: PlutoAutoSizeMode.equal),
                      scrollbar: PlutoGridScrollbarConfig(
                          draggableScrollbar: true,
                          isAlwaysShown: true,
                          scrollbarThickness:
                              PlutoScrollbar.defaultThicknessWhileDragging)),
                ),
              ),
            ),
          ],
        ),
      );
    });
  }

  void subsIntPayoutOnPressed(HomeCtrl ctrl) async {
    setState(() {
      subsIntIsLoading = true;
    });

    try {
      int financialYear = TheFinancialYear.getCurrentFinancialYearStartYear();

      final marchRecoverySnapshot = await FBFireStore.recoverymonthly
          .where('selectedyear', isEqualTo: financialYear)
          .where('selectedmonth', isEqualTo: 3)
          .limit(1)
          .get();

      // if (marchRecoverySnapshot.docs.isEmpty) {
      //   showCtcAppSnackBar(
      //       context, "March recovery not completed. Payout not allowed.");
      //   setState(() {
      //     subsIntIsLoading = false;
      //   });
      //   return;
      // }

      for (final row in rows) {
        // print('user.cpfNo: ${ctrl.users.map((u) => u.cpfNo).toList()}');
        // print(
        //     'row cpf_no: ${row.cells['cpf_no']?.value} (${row.cells['cpf_no']?.value.runtimeType})');
        final user = ctrl.users.firstWhere(
          (element) =>
              element.cpfNo.toString() == row.cells['cpf_no']?.value.toString(),
        );

        // print("users : ${user.cpfNo}");
        // final int currentYear = DateTime.now().year;

        final querySnapshot = await FBFireStore.societyYearly
            .where('selectedyear', isEqualTo: financialYear)
            .limit(1)
            .get();

        final batch = FBFireStore.fb.batch();

        if (user.subsIntPayoutStatus == true) {
          // print("userdoc id 1 : ${user.docId}");
          setState(() {
            subsIntIsLoading = false;
          });
          return;
        } else {
          if (user.subsIntPayoutStatus == false) {
            // print("userdoc id 2 : ${user.docId}");
            batch.update(FBFireStore.users.doc(user.docId), {
              'subsIntPayoutStatus': true,
            });
          } else {
            batch.set(FBFireStore.users.doc(user.docId), {
              'subsIntPayoutStatus': true,
            });
          }

          batch.update(
              FBFireStore.societyYearly.doc(querySnapshot.docs.first.id), {
            'totalSubscription':
                num.tryParse(row.cells['subscription_amount']?.value ?? '0') ??
                    0,
            'intOnSubscription':
                num.tryParse(row.cells['interest_amount']?.value ?? '0') ?? 0,
            'subscriptionInterestRate': num.tryParse(
                    ctrl.settings?.subscriptionInterest.toString() ?? '0') ??
                0,
            'updatedAt': DateTime.now(),
          });

          batch.set(FBFireStore.transactions.doc(), {
            'title': 'Subscription Interest Payout',
            'amount':
                num.tryParse(row.cells['interest_amount']?.value ?? '0') ?? 0,
            'uId': user.docId,
            'createdAt': DateTime.now(),
            'inn': true,
            'userMonthlyId': null,
            'recoveryId': null,
          });

          batch.set(FBFireStore.notifications.doc(), {
            'title': 'Subscription Interest Payout',
            'desc':
                '₹${num.tryParse(row.cells['interest_amount']?.value ?? '0') ?? 0}',
            'uId': user.docId,
            'type': 'Subscription Interest',
            'districtOffice': user.districtoffice,
            'createdAt': DateTime.now(),
          });
        }

        await batch.commit();

        setState(() {
          row.cells['interest_amount']?.value = '0';
          row.cells['status']?.value = 'Paid';
        });
      }
      showCtcAppSnackBar(context, "Payout Successful");
    } catch (e) {
      debugPrint("Error during payout: ${e.toString()}");
      showCtcAppSnackBar(context, "Payout Failed");
    } finally {
      setState(() {
        subsIntIsLoading = false;
      });
    }
  }

  void subsIntInsertRows(List<UserModel> users, HomeCtrl ctrl) {
    if (subsIntSelectedYear == null) {
      showCtcAppSnackBar(context, "Please select a year to view data");
      return;
    }

    final selectedStartYear = int.parse(subsIntSelectedYear!.split('-')[0]);

    final filteredUsers = users.where((user) {
      if (user.registrationDate == null || !user.approved) return false;
      return user.registrationDate!.year <= selectedStartYear;
    }).toList();

    rows.clear();

    if (filteredUsers.isEmpty && !hasShownEmptySnackBar) {
      hasShownEmptySnackBar = true;
      WidgetsBinding.instance.addPostFrameCallback((_) {
        showCtcAppSnackBar(context, "No users available for the selected year");
      });
      return;
    }

    hasShownEmptySnackBar = false;

    for (int i = 0; i < filteredUsers.length; i++) {
      final user = filteredUsers[i];
      final doName = ctrl.districtoffice.firstWhereOrNull(
        (element) => element.docId == user.districtoffice,
      );

      num subsInt =
          num.tryParse(ctrl.settings?.subscriptionInterest ?? '') ?? 0;
      final subscriptionAmount = user.totalSubs ?? 0;
      final interestAmount = subscriptionAmount * subsInt / 100;

      rows.add(PlutoRow(cells: {
        'sr_no': PlutoCell(value: '${i + 1}'),
        'cpf_no': PlutoCell(value: user.cpfNo.toString()),
        'name': PlutoCell(value: user.name),
        'district_office': PlutoCell(value: doName?.name ?? '-'),
        'subscription_amount': PlutoCell(value: subscriptionAmount.toString()),
        'interest_amount': PlutoCell(value: interestAmount.toStringAsFixed(2)),
        'status':
            PlutoCell(value: user.subsIntPayoutStatus ? 'Paid' : 'Unpaid'),
      }));
    }

    setState(() {}); // Trigger PlutoGrid to rebuild
  }

  Future<void> exportToCSV() async {
    setState(() {
      expSubsIntIsLoading = true;
    });

    try {
      if (subsIntSelectedYear == null) {
        showCtcAppSnackBar(context, "Please select a year to export");
        setState(() {
          expSubsIntIsLoading = false;
        });
        return;
      }
      if (rows.isEmpty) {
        showCtcAppSnackBar(context, "No data available to export");
        setState(() {
          expSubsIntIsLoading = false;
        });
        return;
      }
      List<List<dynamic>> csvData = [
        [
          'SR. NO',
          'CPF NO.',
          'NAME OF MEMBER',
          'DISTRICT OFFICE',
          'SUBSCRIPTION AMOUNT',
          'INTEREST AMOUNT',
          'STATUS',
        ],
        ...rows.map((row) => [
              row.cells['sr_no']?.value ?? '',
              row.cells['cpf_no']?.value ?? '',
              row.cells['name']?.value ?? '',
              row.cells['district_office']?.value ?? '',
              row.cells['subscription_amount']?.value ?? '',
              row.cells['interest_amount']?.value ?? '',
              row.cells['status']?.value ?? '',
            ])
      ];

      String csv = const ListToCsvConverter().convert(csvData);
      final bytes = Uint8List.fromList(csv.codeUnits);

      await FileSaver.instance.saveFile(
        name: 'subscription_interest_payout_${subsIntSelectedYear ?? ''}.csv',
        bytes: bytes,
        // ext: 'csv',
        mimeType: MimeType.csv,
      );
      showCtcAppSnackBar(context, "CSV exported successfully");
    } catch (e) {
      showCtcAppSnackBar(context, "CSV export failed: ${e.toString()}");
    } finally {
      setState(() {
        expSubsIntIsLoading = false;
      });
    }
  }
}
