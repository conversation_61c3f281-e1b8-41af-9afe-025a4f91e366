import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:foodcorp_admin/common/page_header.dart';
import 'package:foodcorp_admin/controller/homectrl.dart';
import 'package:foodcorp_admin/models/loan_model.dart';
import 'package:foodcorp_admin/models/user_model.dart';
import 'package:foodcorp_admin/shared/methods.dart';
import 'package:foodcorp_admin/shared/router.dart';
// import 'package:foodcorp_admin/views/Loan/forms/ltloanform.dart';
// import 'package:foodcorp_admin/views/Loan/forms/stloanform.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';

enum SelectedLoan { longtermloan, shorttermloan }

class LoanPage extends StatefulWidget {
  const LoanPage({super.key});

  @override
  State<LoanPage> createState() => _LoanPageState();
}

SearchController sctrl = SearchController();

class _LoanPageState extends State<LoanPage> {
  UserModel? selectedUser;
  SelectedLoan selectedLoan = SelectedLoan.longtermloan;

  @override
  Widget build(BuildContext context) {
    return GetBuilder<HomeCtrl>(builder: (ctrl) {
      List<Loan> filtered = ctrl.loan
          .where((element) => element.rejectionDate == null)
          .where((element) => element.isSettled == false)
          .where((element) => element.settledOn == null)
          .where((element) => element.isSettled == false)
          .where((element) => selectedLoan == SelectedLoan.longtermloan
              ? element.loanType == 'Long Term Loan'
              : element.loanType == 'Emergency Loan')
          .where((loan) {
        final user = ctrl.users.firstWhereOrNull((u) => u.docId == loan.uid);
        final search = sctrl.text.toLowerCase();
        return loan.applicationNo.toString().contains(search) ||
            (user?.name.toLowerCase().contains(search) ?? false);
      }).toList();

      filtered.sort((a, b) => b.approvedOn!.compareTo(a.approvedOn!));

      if (selectedUser != null) {
        filtered = filtered
            .where((element) => element.uid == selectedUser?.docId)
            .toList();
      }

      return SingleChildScrollView(
        padding: const EdgeInsets.all(40),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    CustomSearchBarWidget(
                      searchController: sctrl,
                      searchOnChanged: (p0) {
                        setState(() {});
                      },
                    ),
                    // SizedBox(width: 10),
                    // DropdownSearch<UserModel>(
                    //   selectedItem: selectedUser,
                    //   onChanged: (value) {
                    //     selectedUser = value;
                    //     setState(() {});
                    //   },
                    //   decoratorProps: DropDownDecoratorProps(
                    //     decoration: InputDecoration(
                    //       hintText: "Search User",
                    //       constraints: const BoxConstraints(maxWidth: 450),
                    //       border: OutlineInputBorder(
                    //         borderRadius: BorderRadius.circular(10),
                    //       ),
                    //     ),
                    //   ),
                    //   popupProps: PopupProps.menu(
                    //     showSearchBox: true,
                    //     searchFieldProps: TextFieldProps(
                    //       decoration: InputDecoration(
                    //         hintText: ' Search... ',
                    //         border: UnderlineInputBorder(),
                    //       ),
                    //     ),
                    //   ),
                    //   itemAsString: (item) => item.name,
                    //   items: (filter, loadProps) => ctrl.users,
                    //   compareFn: (item1, item2) =>
                    //       item1.name.toLowerCase() == item2.name.toLowerCase(),
                    // ),
                    // if (selectedUser != null) const SizedBox(width: 5),
                    // if (selectedUser != null)
                    //   IconButton(
                    //     onPressed: () {
                    //       setState(() {
                    //         selectedUser = null;
                    //       });
                    //     },
                    //     icon: const Icon(Icons.clear, size: 30),
                    //   ),
                  ],
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    CustomHeaderButton(
                      onPressed: () => context.push(Routes.addloanpage),
                      buttonName: "Add Loan",
                    ),
                    SizedBox(width: 5),
                    CustomHeaderButton(
                      onPressed: () => context.push(Routes.loanhistory),
                      buttonName: "Loan History",
                    ),
                  ],
                )
              ],
            ),
            const SizedBox(height: 30),
            CupertinoSlidingSegmentedControl<SelectedLoan>(
              thumbColor: Colors.green.shade300,
              groupValue: selectedLoan,
              children: {
                SelectedLoan.longtermloan: Text(
                  "Long Term Loan",
                  style: TextStyle(
                    color: selectedLoan == SelectedLoan.longtermloan
                        ? Colors.white
                        : Colors.black,
                  ),
                ),
                SelectedLoan.shorttermloan: Text(
                  "Emergency Loan",
                  style: TextStyle(
                    color: selectedLoan == SelectedLoan.shorttermloan
                        ? Colors.white
                        : Colors.black,
                  ),
                ),
              },
              onValueChanged: (SelectedLoan? value) {
                if (value != null) {
                  setState(() {
                    selectedLoan = value;
                  });
                }
              },
            ),
            const SizedBox(height: 30),
            Row(
              children: [
                HeaderTxt(txt: 'Sr.no'),
                HeaderTxt(txt: "Approved On"),
                HeaderTxt(txt: "Name"),
                HeaderTxt(txt: "DO Office"),
                HeaderTxt(txt: 'Appl No.'),
                HeaderTxt(txt: 'Loan Amount'),
              ],
            ),
            const SizedBox(height: 30),
            ...List.generate(
              filtered.length,
              (index) {
                final loan = filtered[index];
                final user = ctrl.users
                    .firstWhereOrNull((element) => element.docId == loan.uid);
                final doOffice = ctrl.districtoffice.firstWhereOrNull(
                    (element) => element.docId == user?.districtoffice);

                return InkWell(
                  onTap: () => context.push(
                    '${Routes.loanDetails}/${loan.docId}',
                  ),
                  child: Container(
                    height: 40,
                    color: index % 2 == 0 ? Colors.white : null,
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 10.0),
                      child: Row(
                        children: [
                          Expanded(child: Text("${index + 1}")),
                          Expanded(
                            child: Text(
                              DateFormat('dd-MM-yyyy').format(loan.approvedOn!),
                            ),
                          ),
                          Expanded(child: Text(user?.name ?? "")),
                          Expanded(child: Text(doOffice?.name ?? "-")),
                          Expanded(child: Text(loan.applicationNo.toString())),
                          Expanded(child: Text(loan.appliedLoanAmt.toString())),
                          // Expanded(child: Text(loan.loanType)),
                        ],
                      ),
                    ),
                  ),
                );
              },
            ),
          ],
        ),
      );
    });
  }

  // Future<dynamic> applDetailsDialog(
  //   BuildContext context,
  //   HomeCtrl ctrl,
  //   int index,
  // ) {
  //   return showDialog(
  //     context: context,
  //     builder: (context) => AlertDialog(
  //       backgroundColor: Colors.white,
  //       scrollable: true,
  //       content: SingleChildScrollView(
  //         child: ConstrainedBox(
  //           constraints: const BoxConstraints(maxHeight: 400),
  //           child: SizedBox(
  //             width: 800,
  //             child: (ctrl.loan[index].loanType == 'Long Term Loan')
  //                 ? ApprovedLtloanform(
  //                     loan: ctrl.loan[index],
  //                     index: index,
  //                   )
  //                 : ApprovedStloanform(
  //                     loan: ctrl.loan[index],
  //                     index: index,
  //                   ),
  //           ),
  //         ),
  //       ),
  //       title: const Text("Application Details"),
  //     ),
  //   );
  // }
}
