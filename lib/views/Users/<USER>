// ignore_for_file: use_build_context_synchronously, avoid_print
import 'dart:typed_data';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:file_picker/file_picker.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:foodcorp_admin/common/page_header.dart';
import 'package:foodcorp_admin/controller/homectrl.dart';
import 'package:foodcorp_admin/models/loan_model.dart';
import 'package:foodcorp_admin/models/user_model.dart';
import 'package:foodcorp_admin/shared/const.dart';
import 'package:foodcorp_admin/shared/firebase.dart';
import 'package:foodcorp_admin/shared/methods.dart';
import 'package:foodcorp_admin/shared/router.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:image_picker/image_picker.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:url_launcher/url_launcher_string.dart';

class AddUserformPage extends StatefulWidget {
  const AddUserformPage({super.key, this.userdocId});
  final String? userdocId;
  // final String? loandocId;

  @override
  State<AddUserformPage> createState() => _AddUserformPageState();
}

class _AddUserformPageState extends State<AddUserformPage> {
  QuerySnapshot<Map<String, dynamic>>? fbUserRecoveryMonthlyDataFetch;
  QuerySnapshot<Map<String, dynamic>>? fbUserMonthlyDataFetch;
  QuerySnapshot<Map<String, dynamic>>? fbSocietyYearlyDataFetch;

  UserModel? usermodelw;

  TextEditingController nctrl = TextEditingController();
  TextEditingController cpfnoctrl = TextEditingController();
  TextEditingController doctrl = TextEditingController();
  TextEditingController phonectrl = TextEditingController();
  TextEditingController emailctrl = TextEditingController();
  TextEditingController totalsubsctrl = TextEditingController();
  TextEditingController settlementctrl = TextEditingController();
  TextEditingController totalSubsIntctrl = TextEditingController();
  TextEditingController ltLoansDuectrl = TextEditingController();
  TextEditingController stLoansDuectrl = TextEditingController();
  TextEditingController totalLtLoansctrl = TextEditingController();
  TextEditingController totalStLoansctrl = TextEditingController();
  TextEditingController totalLtIntPaidctrl = TextEditingController();
  TextEditingController totalStIntPaidctrl = TextEditingController();
  TextEditingController totalDividentctrl = TextEditingController();
  TextEditingController totalSharesctrl = TextEditingController();
  TextEditingController employeenoctrl = TextEditingController();
  TextEditingController addressctrl = TextEditingController();
  TextEditingController paddressctrl = TextEditingController();
  TextEditingController bankacnamectrl = TextEditingController();
  TextEditingController banknamectrl = TextEditingController();
  TextEditingController ifsccodectrl = TextEditingController();
  TextEditingController bankacnoctrl = TextEditingController();
  TextEditingController nomineeNamectrl = TextEditingController();
  TextEditingController nomineeRelationctrl = TextEditingController();

  //previous month record controller
  TextEditingController duesCtrl = TextEditingController();
  TextEditingController penaltyCtrl = TextEditingController();
  TextEditingController userPrevYearCtrl = TextEditingController();
  TextEditingController userPrevMonthCtrl = TextEditingController();
  TextEditingController userPrevObltCtrl = TextEditingController();
  TextEditingController userPrevObstCtrl = TextEditingController();
  TextEditingController userPrevCbltCtrl = TextEditingController();
  TextEditingController userPrevCbstCtrl = TextEditingController();
  TextEditingController userPrevLoanTotalSecondCtrl = TextEditingController();
  TextEditingController userPrevSubsCtrl = TextEditingController();
  TextEditingController userPrevLtInstallmentCtrl = TextEditingController();
  TextEditingController userPrevStInstallmentCtrl = TextEditingController();
  TextEditingController userPrevInterestCtrl = TextEditingController();
  TextEditingController userPrevTotalLoanCtrl = TextEditingController();
  TextEditingController userPrevInstallmentReceivedCtrl =
      TextEditingController();

  //paid details (Receipt)
  TextEditingController userPrevLtLoanpaidCtrl = TextEditingController();
  TextEditingController userPrevStLoanpaidCtrl = TextEditingController();
  TextEditingController userPrevLtInstallmentpaidCtrl = TextEditingController();
  TextEditingController userPrevStInstallmentpaidCtrl = TextEditingController();
  TextEditingController userPrevLtInterestpaidCtrl = TextEditingController();
  TextEditingController userPrevStInterestpaidCtrl = TextEditingController();
  TextEditingController userPrevPenaltyPaidCtrl = TextEditingController();
  TextEditingController userPrevSubsPaidCtrl = TextEditingController();

  //long term loan controllers
  TextEditingController ltloanTypeCtrl = TextEditingController();
  TextEditingController ltloanAppNoCtrl = TextEditingController();
  TextEditingController ltloanAppliedLoanAmtCtrl =
      TextEditingController(); // lloanAppliedLoanAmtCtrl
  TextEditingController ltloanTotalAmtCtrl = TextEditingController();
  TextEditingController ltloanTotalLoanPaidCtrl = TextEditingController();
  TextEditingController ltloanTotalLoanDueCtrl = TextEditingController();
  TextEditingController ltloanShareCtrl = TextEditingController();
  TextEditingController ltloanTotalInterestPaidCtrl = TextEditingController();
  TextEditingController ltloanDesigationCtrl = TextEditingController();
  TextEditingController ltloanBankAccNumCtrl = TextEditingController();
  TextEditingController ltloanAppliedLoanAmtInWordsCtrl =
      TextEditingController();
  TextEditingController ltloanReasonCtrl = TextEditingController();
  TextEditingController ltloanLtSurityName1Ctrl = TextEditingController();
  TextEditingController ltloanLtSurityName2Ctrl = TextEditingController();
  TextEditingController ltloanLtSurityAcc1Ctrl = TextEditingController();
  TextEditingController ltloanLtSurityAcc2Ctrl = TextEditingController();
  TextEditingController ltloanMonthlyInstallmentAmtCtrl =
      TextEditingController();

  //short term loan controllers
  TextEditingController stloanTypeCtrl = TextEditingController();
  TextEditingController stloanBalanceCtrl = TextEditingController();
  TextEditingController stloanAppNoCtrl = TextEditingController();
  TextEditingController stloanTotalLoanAmtCtrl = TextEditingController();
  TextEditingController stloanAppliedLoanAmtCtrl = TextEditingController();
  TextEditingController stloanTotalLoanPaidCtrl = TextEditingController();
  TextEditingController stloanTotalLoanDueCtrl = TextEditingController();
  TextEditingController stloanShareCtrl = TextEditingController();
  TextEditingController stloanTotalInterestPaidCtrl = TextEditingController();
  // TextEditingController stloanDesigationCtrl = TextEditingController();
  TextEditingController stloanBankAccNumCtrl = TextEditingController();
  TextEditingController stloanAppliedLoanAmtInWordsCtrl =
      TextEditingController();
  TextEditingController stloanReasonCtrl = TextEditingController();
  TextEditingController stloanLtSurityName1Ctrl = TextEditingController();
  TextEditingController stloanLtSurityName2Ctrl = TextEditingController();
  // TextEditingController stloanLtSurityAcc1Ctrl = TextEditingController();
  // TextEditingController stloanLtSurityAcc2Ctrl = TextEditingController();
  TextEditingController stloanMonthlyInstallmentAmtCtrl =
      TextEditingController();

  String? pdfDoc;
  String? documentUrl;
  String? selectedDocumentName;
  String? selectedofficeid;
  bool isLoading = false;
  bool fullLoader = true;
  final picker = ImagePicker();
  UploadTask? uploadTask;
  int? selectedYear;
  int? selectedMonth;
  DateTime? installmentRecDate;
  DateTime? ltAppliedOnDate;
  DateTime? ltApprovedOnDate;
  DateTime? stAppliedOnDate;
  DateTime? stApprovedOnDate;
  // String? selectedLoanType;
  String? ltloanUserSignuploadedUrl;
  String? stloanUserSignuploadedUrl;

  String? ltloanUserDocUploadedUrl;
  String? stloanUserDocUploadedUrl;
  bool ltLoanCheck = false;
  bool stLoanCheck = false;

  Loan? longTermLoanModel;
  Loan? shortTermLoanModel;

  bool isUploadingltBSign = false;
  bool isUploadingStBSign = false;

  bool isUploadingltDoc = false;
  bool isUploadingStDoc = false;

  bool isUploadingDoc = false;

  @override
  void initState() {
    super.initState();

    getData();

    userPrevObltCtrl
        .addListener(() => calculateLoanDetails(Get.find<HomeCtrl>()));

    userPrevObstCtrl
        .addListener(() => calculateLoanDetails(Get.find<HomeCtrl>()));

    userPrevLtLoanpaidCtrl
        .addListener(() => calculateLoanDetails(Get.find<HomeCtrl>()));

    userPrevStLoanpaidCtrl
        .addListener(() => calculateLoanDetails(Get.find<HomeCtrl>()));

    // -------

    userPrevSubsCtrl
        .addListener(() => calculateLoanDetails(Get.find<HomeCtrl>()));
    userPrevLtInstallmentCtrl
        .addListener(() => calculateLoanDetails(Get.find<HomeCtrl>()));
    userPrevStInstallmentCtrl
        .addListener(() => calculateLoanDetails(Get.find<HomeCtrl>()));
    userPrevInterestCtrl
        .addListener(() => calculateLoanDetails(Get.find<HomeCtrl>()));
    duesCtrl.addListener(() => calculateLoanDetails(Get.find<HomeCtrl>()));
    penaltyCtrl.addListener(() => calculateLoanDetails(Get.find<HomeCtrl>()));

// --- Lt

    ltloanTotalAmtCtrl
        .addListener(() => calculateLoanDetails(Get.find<HomeCtrl>()));

    ltloanTotalLoanPaidCtrl
        .addListener(() => calculateLoanDetails(Get.find<HomeCtrl>()));

// --- Lt

    stloanAppliedLoanAmtCtrl
        .addListener(() => calculateLoanDetails(Get.find<HomeCtrl>()));

    stloanTotalLoanPaidCtrl
        .addListener(() => calculateLoanDetails(Get.find<HomeCtrl>()));

    ltloanMonthlyInstallmentAmtCtrl
        .addListener(() => calculateLoanDetails(Get.find<HomeCtrl>()));

    stloanMonthlyInstallmentAmtCtrl
        .addListener(() => calculateLoanDetails(Get.find<HomeCtrl>()));

    ltloanTotalInterestPaidCtrl
        .addListener(() => calculateLoanDetails(Get.find<HomeCtrl>()));

    stloanTotalInterestPaidCtrl
        .addListener(() => calculateLoanDetails(Get.find<HomeCtrl>()));
  }

  @override
  void dispose() {
    nctrl.dispose();
    cpfnoctrl.dispose();
    doctrl.dispose();
    phonectrl.dispose();
    emailctrl.dispose();
    totalsubsctrl.dispose();
    settlementctrl.dispose();
    totalSubsIntctrl.dispose();
    ltLoansDuectrl.dispose();
    stLoansDuectrl.dispose();
    totalLtLoansctrl.dispose();
    totalStLoansctrl.dispose();
    totalLtIntPaidctrl.dispose();
    totalStIntPaidctrl.dispose();
    totalDividentctrl.dispose();
    totalSharesctrl.dispose();
    employeenoctrl.dispose();
    addressctrl.dispose();
    paddressctrl.dispose();
    bankacnamectrl.dispose();
    banknamectrl.dispose();
    ifsccodectrl.dispose();
    bankacnoctrl.dispose();
    duesCtrl.dispose();
    penaltyCtrl.dispose();
    nomineeNamectrl.dispose();
    nomineeRelationctrl.dispose();
    userPrevYearCtrl.dispose();
    userPrevMonthCtrl.dispose();
    userPrevObltCtrl.dispose();
    userPrevObstCtrl.dispose();
    userPrevCbltCtrl.dispose();
    userPrevCbstCtrl.dispose();
    userPrevLoanTotalSecondCtrl.dispose();
    userPrevSubsCtrl.dispose();
    userPrevLtInstallmentCtrl.dispose();
    userPrevStInstallmentCtrl.dispose();
    userPrevInterestCtrl.dispose();
    userPrevTotalLoanCtrl.dispose();
    userPrevInstallmentReceivedCtrl.dispose();
    userPrevLtLoanpaidCtrl.dispose();
    userPrevStLoanpaidCtrl.dispose();
    userPrevLtInstallmentpaidCtrl.dispose();
    userPrevStInstallmentpaidCtrl.dispose();
    userPrevLtInterestpaidCtrl.dispose();
    userPrevStInterestpaidCtrl.dispose();
    userPrevPenaltyPaidCtrl.dispose();
    userPrevSubsPaidCtrl.dispose();
    ltloanTypeCtrl.dispose();
    ltloanAppNoCtrl.dispose();
    ltloanAppliedLoanAmtCtrl.dispose();
    ltloanTotalAmtCtrl.dispose();
    ltloanTotalLoanPaidCtrl.dispose();
    ltloanTotalLoanDueCtrl.dispose();
    ltloanShareCtrl.dispose();
    ltloanTotalInterestPaidCtrl.dispose();
    ltloanDesigationCtrl.dispose();
    ltloanBankAccNumCtrl.dispose();
    ltloanAppliedLoanAmtInWordsCtrl.dispose();
    ltloanReasonCtrl.dispose();
    ltloanLtSurityName1Ctrl.dispose();
    ltloanLtSurityName2Ctrl.dispose();
    ltloanLtSurityAcc1Ctrl.dispose();
    ltloanLtSurityAcc2Ctrl.dispose();
    ltloanMonthlyInstallmentAmtCtrl.dispose();
    stloanTypeCtrl.dispose();
    stloanBalanceCtrl.dispose();
    stloanAppNoCtrl.dispose();
    stloanTotalLoanAmtCtrl.dispose();
    stloanAppliedLoanAmtCtrl.dispose();
    stloanTotalLoanPaidCtrl.dispose();
    stloanTotalLoanDueCtrl.dispose();
    stloanShareCtrl.dispose();
    stloanTotalInterestPaidCtrl.dispose();
    stloanBankAccNumCtrl.dispose();
    stloanAppliedLoanAmtInWordsCtrl.dispose();
    stloanReasonCtrl.dispose();
    stloanLtSurityName1Ctrl.dispose();
    stloanLtSurityName2Ctrl.dispose();
    stloanMonthlyInstallmentAmtCtrl.dispose();
    super.dispose();
  }

  getData() async {
    setState(() => fullLoader = true);
    try {
      if (widget.userdocId != null) {
        final userData = await FBFireStore.users.doc(widget.userdocId).get();
        usermodelw = UserModel.fromSnap(userData);
        final loanData = await FBFireStore.loan
            .where('uid', isEqualTo: widget.userdocId)
            .get();

        if (loanData.docs.isNotEmpty) {
          for (var doc in loanData.docs) {
            final loan = Loan.fromSnap(doc);
            if (loan.loanType == LoanTypes.longTerm) {
              longTermLoanModel = loan;
              ltLoanCheck = true;
            } else if (loan.loanType == LoanTypes.emergencyLoan) {
              shortTermLoanModel = loan;
              stLoanCheck = true;
            }
          }
        } else {
          ltLoanCheck = false;
          stLoanCheck = false;
          // return showCtcAppSnackBar(context, "message");
        }
        loadData();
      }
    } catch (e) {
      debugPrint(e.toString());
    }
    setState(() => fullLoader = false);
  }

  loadData() async {
    //user data
    nctrl.text = usermodelw?.name.toUpperCase() ?? "";
    cpfnoctrl.text = usermodelw?.cpfNo.toString() ?? "";
    selectedofficeid = usermodelw?.districtoffice ?? "";
    phonectrl.text = usermodelw?.phoneNo ?? "";
    emailctrl.text = usermodelw?.email ?? "";
    totalsubsctrl.text = usermodelw?.totalSubs.toString() ?? "";
    settlementctrl.text = usermodelw?.settlement.toString() ?? "";
    totalSubsIntctrl.text = usermodelw?.totalSubsInt.toString() ?? "";
    ltLoansDuectrl.text = usermodelw?.ltLoansDue.toString() ?? "";
    stLoansDuectrl.text = usermodelw?.stLoansDue.toString() ?? "";
    totalLtLoansctrl.text = usermodelw?.totalLtLoans.toString() ?? "";
    totalStLoansctrl.text = usermodelw?.totalStLoans.toString() ?? "";
    totalLtIntPaidctrl.text = usermodelw?.totalLtIntPaid.toString() ?? "";
    totalStIntPaidctrl.text = usermodelw?.totalStIntPaid.toString() ?? "";
    totalDividentctrl.text = usermodelw?.totalDivident.toString() ?? "";
    totalSharesctrl.text = usermodelw?.totalShares.toString() ?? "";
    employeenoctrl.text = usermodelw?.employeeNo.toString() ?? "";
    addressctrl.text = usermodelw?.currentAddress.toString() ?? "";
    paddressctrl.text = usermodelw?.permanentAddress.toString() ?? "";
    documentUrl = usermodelw?.documents.toString();
    bankacnamectrl.text = usermodelw?.bankAcName.toString() ?? "";
    banknamectrl.text = usermodelw?.bankName.toString() ?? "";
    ifsccodectrl.text = usermodelw?.ifscCode.toString() ?? "";
    bankacnoctrl.text = usermodelw?.bankAcNo.toString() ?? "";
    duesCtrl.text =
        usermodelw?.userPrevoiusMonthlyRecord?['dues']?.toString() ?? '0';
    penaltyCtrl.text =
        usermodelw?.userPrevoiusMonthlyRecord?['penalty']?.toString() ?? '0';
    '0';
    nomineeNamectrl.text = usermodelw?.nomineeName.toString() ?? "";
    nomineeRelationctrl.text = usermodelw?.nomineeRelation.toString() ?? "";

    //userPrevoiusMonthlyRecord Data

    userPrevObltCtrl.text =
        usermodelw?.userPrevoiusMonthlyRecord?['obLt']?.toString() ?? '0';
    userPrevObstCtrl.text =
        usermodelw?.userPrevoiusMonthlyRecord?['obSt']?.toString() ?? '0';
    userPrevCbltCtrl.text =
        usermodelw?.userPrevoiusMonthlyRecord?['ltCb']?.toString() ?? '0';
    userPrevCbstCtrl.text =
        usermodelw?.userPrevoiusMonthlyRecord?['stCb']?.toString() ?? '0';
    userPrevLtInstallmentCtrl.text =
        usermodelw?.userPrevoiusMonthlyRecord?['ltInstallment']?.toString() ??
            '0';
    userPrevStInstallmentCtrl.text =
        usermodelw?.userPrevoiusMonthlyRecord?['stInstallment']?.toString() ??
            '0';
    userPrevTotalLoanCtrl.text =
        usermodelw?.userPrevoiusMonthlyRecord?['loanTotal']?.toString() ?? '0';
    userPrevLoanTotalSecondCtrl.text =
        usermodelw?.userPrevoiusMonthlyRecord?['total']?.toString() ?? '0';
    userPrevSubsCtrl.text =
        usermodelw?.userPrevoiusMonthlyRecord?['subs']?.toString() ?? '0';
    userPrevInterestCtrl.text =
        usermodelw?.userPrevoiusMonthlyRecord?['interest']?.toString() ?? '0';
    userPrevInstallmentReceivedCtrl.text =
        usermodelw?.userPrevoiusMonthlyRecord?['installmentRec']?.toString() ??
            '0';
    userPrevLtLoanpaidCtrl.text =
        usermodelw?.userPrevoiusMonthlyRecord?['loanPaidLt']?.toString() ?? '0';
    userPrevStLoanpaidCtrl.text =
        usermodelw?.userPrevoiusMonthlyRecord?['loanPaidst']?.toString() ?? '0';
    userPrevLtInstallmentpaidCtrl.text = usermodelw
            ?.userPrevoiusMonthlyRecord?['longTermInstalmentPaid']
            ?.toString() ??
        '0';
    userPrevStInstallmentpaidCtrl.text = usermodelw
            ?.userPrevoiusMonthlyRecord?['shortTermInstalmentPaid']
            ?.toString() ??
        '0';
    userPrevLtInterestpaidCtrl.text = usermodelw
            ?.userPrevoiusMonthlyRecord?['longTermInterestPaid']
            ?.toString() ??
        '0';
    userPrevStInterestpaidCtrl.text = usermodelw
            ?.userPrevoiusMonthlyRecord?['shortTermInterestPaid']
            ?.toString() ??
        '0';
    userPrevPenaltyPaidCtrl.text =
        usermodelw?.userPrevoiusMonthlyRecord?['penaltyPaid']?.toString() ??
            '0';
    userPrevSubsPaidCtrl.text = usermodelw
            ?.userPrevoiusMonthlyRecord?['subscriptionPaid']
            ?.toString() ??
        '0';
    selectedMonth = usermodelw?.userPrevoiusMonthlyRecord?['selectedmonth'];
    selectedYear = usermodelw?.userPrevoiusMonthlyRecord?['selectedyear'];
    var dbRecDate =
        usermodelw?.userPrevoiusMonthlyRecord?['installmentRecDate'];
    installmentRecDate = dbRecDate != null
        ? (dbRecDate is Timestamp
            ? dbRecDate.toDate()
            : DateTime.tryParse(dbRecDate.toString()))
        : null;

    //long term loan Details

    ltloanTypeCtrl.text = longTermLoanModel?.loanType.toString() ?? "";
    ltloanAppNoCtrl.text = longTermLoanModel?.applicationNo.toString() ?? "";
    ltloanAppliedLoanAmtCtrl.text =
        longTermLoanModel?.totalLoanAmt.toString() ?? "";
    ltloanTotalAmtCtrl.text =
        longTermLoanModel?.appliedLoanAmt.toString() ?? "";
    ltloanTotalLoanPaidCtrl.text =
        longTermLoanModel?.totalLoanPaid.toString() ?? "";
    ltloanTotalLoanDueCtrl.text =
        longTermLoanModel?.totalLoanDue.toString() ?? "";
    ltloanShareCtrl.text = longTermLoanModel?.share.toString() ?? "";
    ltloanTotalInterestPaidCtrl.text =
        longTermLoanModel?.totalInterestPaid.toString() ?? "";
    ltloanDesigationCtrl.text = longTermLoanModel?.designation.toString() ?? "";
    ltloanBankAccNumCtrl.text = longTermLoanModel?.bAcNo.toString() ?? "";
    ltloanAppliedLoanAmtInWordsCtrl.text =
        longTermLoanModel?.appliedLoanAmt.toString() ?? "";
    ltloanReasonCtrl.text = longTermLoanModel?.loanReason.toString() ?? "";
    ltloanLtSurityName1Ctrl.text =
        longTermLoanModel?.surityName1.toString() ?? "";
    ltloanLtSurityName2Ctrl.text =
        longTermLoanModel?.surityName2.toString() ?? "";
    ltloanLtSurityAcc1Ctrl.text = longTermLoanModel?.sAcNo1.toString() ?? "";
    ltloanLtSurityAcc2Ctrl.text = longTermLoanModel?.sAcNo2.toString() ?? "";
    ltloanMonthlyInstallmentAmtCtrl.text =
        longTermLoanModel?.monthlyInstallmentAmt.toString() ?? "";
    ltloanUserSignuploadedUrl = longTermLoanModel?.bSign.toString();
    ltloanUserDocUploadedUrl = longTermLoanModel?.pdfString;
    var dbAppliedOn = longTermLoanModel?.appliedOn;
    ltAppliedOnDate = dbAppliedOn != null
        ? (dbAppliedOn is Timestamp
            ? dbAppliedOn
            : DateTime.tryParse(dbAppliedOn.toString()))
        : null;
    var dbApprovedOn = longTermLoanModel?.approvedOn;
    ltApprovedOnDate = dbApprovedOn != null
        ? (dbApprovedOn is Timestamp
            ? dbApprovedOn
            : DateTime.tryParse(dbApprovedOn.toString()))
        : null;
    // var appliedOnDate = longTermLoanModel?.appliedOn ??
    //     DateTime.tryParse(longTermLoanModel?.appliedOn.toString() ?? "");

    //short term loan Details
    stloanTypeCtrl.text = shortTermLoanModel?.loanType.toString() ?? "";
    stloanAppNoCtrl.text = shortTermLoanModel?.applicationNo.toString() ?? "";
    stloanTotalLoanAmtCtrl.text =
        shortTermLoanModel?.totalLoanAmt.toString() ?? "";
    stloanAppliedLoanAmtCtrl.text =
        shortTermLoanModel?.appliedLoanAmt.toString() ?? "";
    stloanTotalLoanPaidCtrl.text =
        shortTermLoanModel?.totalLoanPaid.toString() ?? "";
    stloanTotalLoanDueCtrl.text =
        shortTermLoanModel?.totalLoanDue.toString() ?? "";
    stloanShareCtrl.text = shortTermLoanModel?.share.toString() ?? "";
    stloanTotalInterestPaidCtrl.text =
        shortTermLoanModel?.totalInterestPaid.toString() ?? "";
    // stloanDesigationCtrl.text = shortTermLoanModel!.designation.toString();
    stloanBankAccNumCtrl.text = shortTermLoanModel?.bAcNo.toString() ?? "";
    stloanAppliedLoanAmtInWordsCtrl.text =
        shortTermLoanModel?.appliedLoanAmtinWords.toString() ?? "";
    stloanReasonCtrl.text = shortTermLoanModel?.loanReason.toString() ?? "";
    stloanLtSurityName1Ctrl.text =
        shortTermLoanModel?.surityName1.toString() ?? "";
    stloanLtSurityName2Ctrl.text =
        shortTermLoanModel?.surityName2.toString() ?? "";
    // stloanBalanceCtrl.text = shortTermLoanModel?.balance.toString() ?? "0";
    // stloanLtSurityAcc1Ctrl.text = shortTermLoanModel!.sAcNo1.toString();
    // stloanLtSurityAcc2Ctrl.text = shortTermLoanModel!.sAcNo2.toString();
    stloanMonthlyInstallmentAmtCtrl.text =
        shortTermLoanModel?.monthlyInstallmentAmt.toString() ?? "";
    stloanUserSignuploadedUrl = shortTermLoanModel?.bSign.toString();
    stloanUserDocUploadedUrl = shortTermLoanModel?.pdfString;
    var dbSlAppliedOn = shortTermLoanModel?.appliedOn;
    stAppliedOnDate = dbSlAppliedOn != null
        ? (dbSlAppliedOn is Timestamp
            ? dbSlAppliedOn
            : DateTime.tryParse(dbSlAppliedOn.toString()))
        : null;
    var dbSlApprovedOn = shortTermLoanModel?.approvedOn;
    stApprovedOnDate = dbSlApprovedOn != null
        ? (dbSlApprovedOn is Timestamp
            ? dbSlApprovedOn
            : DateTime.tryParse(dbSlApprovedOn.toString()))
        : null;
  }

  void calculateLoanDetails(HomeCtrl ctrl) {
    num ltappliedamt = num.tryParse(ltloanTotalAmtCtrl.text) ?? 0;
    num lttotalpaid = num.tryParse(ltloanTotalLoanPaidCtrl.text) ?? 0;
    num ltintpaid = num.tryParse(ltloanTotalInterestPaidCtrl.text) ?? 0;
    num lttotaldue = ltappliedamt - lttotalpaid;
    ltLoansDuectrl.text = lttotaldue.ceil().toString();
    totalLtLoansctrl.text = ltappliedamt.ceil().toString();
    totalLtIntPaidctrl.text = ltintpaid.ceil().toString();
    // userPrevObltCtrl.text = ltappliedamt.ceil().toString();
    ltloanTotalLoanDueCtrl.text = lttotaldue.ceil().toString();
    ltloanShareCtrl.text = ((ltappliedamt * 10) / 100).ceil().toString();

    // ---

    num stappliedamt = num.tryParse(stloanAppliedLoanAmtCtrl.text) ?? 0;
    num sttotalpaid = num.tryParse(stloanTotalLoanPaidCtrl.text) ?? 0;
    num stintpaid = num.tryParse(stloanTotalInterestPaidCtrl.text) ?? 0;
    num sttotaldue = stappliedamt - sttotalpaid;
    stLoansDuectrl.text = sttotaldue.ceil().toString();
    totalStLoansctrl.text = stappliedamt.ceil().toString();
    totalStIntPaidctrl.text = stintpaid.ceil().toString();
    // userPrevObstCtrl.text = stappliedamt.toStringAsFixed(2);
    stloanTotalLoanDueCtrl.text = sttotaldue.ceil().toString();
    stloanShareCtrl.text = ((stappliedamt * 10) / 100).ceil().toString();

    // ---

    num oblt = num.tryParse(userPrevObltCtrl.text) ?? 0;
    num obst = num.tryParse(userPrevObstCtrl.text) ?? 0;
    num ltPaid = num.tryParse(userPrevLtLoanpaidCtrl.text) ?? 0;
    num stPaid = num.tryParse(userPrevStLoanpaidCtrl.text) ?? 0;

    num total = oblt + obst + ltPaid + stPaid;

    userPrevTotalLoanCtrl.text = total.ceil().toString();

    // -------

    num subs = num.tryParse(userPrevSubsCtrl.text) ?? 0;
    num ltinstallment = num.tryParse(userPrevLtInstallmentCtrl.text) ?? 0;
    num stinstallment = num.tryParse(userPrevStInstallmentCtrl.text) ?? 0;
    num interest = num.tryParse(userPrevInterestCtrl.text) ?? 0;
    num dues = num.tryParse(duesCtrl.text) ?? 0;
    num penalty = num.tryParse(penaltyCtrl.text) ?? 0;
    num total2 =
        subs + ltinstallment + stinstallment + interest + dues + penalty;

    userPrevLoanTotalSecondCtrl.text = total2.ceil().toString();

    // -------

    num ltcb = oblt - ltinstallment;
    num stcb = obst - stinstallment;

    userPrevCbltCtrl.text = ltcb.ceil().toString();
    userPrevCbstCtrl.text = stcb.ceil().toString();

    userPrevSubsPaidCtrl.text = subs.ceil().toString();
    userPrevInstallmentReceivedCtrl.text = total2.ceil().toString();
    userPrevLtInstallmentpaidCtrl.text = ltinstallment.ceil().toString();
    userPrevStInstallmentpaidCtrl.text = stinstallment.ceil().toString();
  }

  Uint8List? pdfBytes;
  String? pdfFileName;

  Future<void> pickFiles() async {
    FilePickerResult? result = await FilePicker.platform.pickFiles(
      type: FileType.custom,
      allowMultiple: false,
      allowedExtensions: ['pdf'],
      withData: true,
    );

    if (result != null && result.files.isNotEmpty) {
      final file = result.files.first;

      if (file.bytes != null && file.size <= 3 * 1024 * 1024) {
        setState(() {
          pdfBytes = file.bytes;
          // Use a timestamp to make the filename unique
          pdfFileName = '${DateTime.now().millisecondsSinceEpoch}_${file.name}';
        });
        // print("File selected: $pdfFileName");
      } else {
        // print("File is too large or unreadable.");
      }
    } else {
      // print("No file selected.");
    }
  }

  Future<String> uploadFile(
      {required bool isLt, required bool isSignature}) async {
    String docUrl = "";

    try {
      if (pdfBytes == null || pdfFileName == null) {
        // print("No file data available.");
        return docUrl;
      }

      // Set loading flag
      if (isLt) {
        setState(() =>
            isSignature ? isUploadingltBSign = true : isUploadingltDoc = true);
      } else {
        setState(() =>
            isSignature ? isUploadingStBSign = true : isUploadingStDoc = true);
      }

      final ref =
          FirebaseStorage.instance.ref().child('documents/$pdfFileName');
      uploadTask = ref.putData(pdfBytes!);
      final snapshot = await uploadTask!.whenComplete(() {});
      final urlDownload = await snapshot.ref.getDownloadURL();

      // print('File uploaded: $urlDownload');

      setState(() {
        if (isLt) {
          if (isSignature) {
            ltloanUserSignuploadedUrl = urlDownload;
          } else {
            ltloanUserDocUploadedUrl = urlDownload;
          }
        } else {
          if (isSignature) {
            stloanUserSignuploadedUrl = urlDownload;
          } else {
            stloanUserDocUploadedUrl = urlDownload;
          }
        }

        pdfBytes = null;
        pdfFileName = null;
      });

      docUrl = urlDownload;
    } catch (e) {
      debugPrint("Error uploading file: ${e.toString()}");
    } finally {
      if (isLt) {
        setState(() => isSignature
            ? isUploadingltBSign = false
            : isUploadingltDoc = false);
      } else {
        setState(() => isSignature
            ? isUploadingStBSign = false
            : isUploadingStDoc = false);
      }
    }

    return docUrl;
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<HomeCtrl>(builder: (ctrl) {
      List<int> yearList = List.generate(
          DateTime.now().year - 2023, (index) => 2023 + index + 1);

      ltloanTypeCtrl.text = LoanTypes.longTerm;
      stloanTypeCtrl.text = LoanTypes.emergencyLoan;

      return isLoading || fullLoader
          ? const Center(
              child: SizedBox(
                  height: 30,
                  width: 30,
                  child: CircularProgressIndicator(color: Colors.black)))
          : Stack(
              children: [
                SingleChildScrollView(
                  padding: const EdgeInsets.all(40),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const SizedBox(height: 20),

                      //personal details

                      PersonalDetailsSection(
                        nctrl: nctrl,
                        cpfnoctrl: cpfnoctrl,
                        employeenoctrl: employeenoctrl,
                        addressctrl: addressctrl,
                        paddressctrl: paddressctrl,
                        selectedofficeid: selectedofficeid,
                        onOfficeChanged: (value) =>
                            setState(() => selectedofficeid = value),
                        districtOffices: ctrl.districtoffice,
                        phonectrl: phonectrl,
                        emailctrl: emailctrl,
                        isAddMode: widget.userdocId == null,
                      ),

                      const SizedBox(height: 20),

                      //bank details

                      BankDetailsSection(
                        bankacnamectrl: bankacnamectrl,
                        banknamectrl: banknamectrl,
                        bankacnoctrl: bankacnoctrl,
                        ifsccodectrl: ifsccodectrl,
                      ),

                      //others

                      const SizedBox(height: 20),

                      OthersSection(
                        totalSharesctrl: totalSharesctrl,
                        // totalDividentctrl: totalDividentctrl,
                      ),

                      const SizedBox(height: 20),

                      //document
                      DocumentSection(
                        userdocId: widget.userdocId,
                        usermodelw: usermodelw,
                        documentUrl: documentUrl,
                        isUploadingDoc: isUploadingDoc,
                        onUpload: () async {
                          FilePickerResult? result =
                              await FilePicker.platform.pickFiles(
                            withData: true,
                            type: FileType.custom,
                            allowMultiple: false,
                            allowedExtensions: ['pdf'],
                          );
                          if (result != null && result.files.isNotEmpty) {
                            var file = result.files.single;
                            var fileBytes = file.bytes;
                            int fileSizeInBytes = fileBytes?.length ?? 0;
                            int maxSizeInBytes = 3 * 1024 * 1024;
                            if (fileSizeInBytes <= maxSizeInBytes) {
                              try {
                                setState(() => isUploadingDoc = true);
                                String fileName =
                                    "documents/${DateTime.now().millisecondsSinceEpoch}_${file.name}";
                                final ref = FirebaseStorage.instance
                                    .ref()
                                    .child(fileName);
                                await ref.putData(fileBytes!);
                                String downloadUrl = await ref.getDownloadURL();
                                setState(() {
                                  documentUrl = downloadUrl;
                                });
                                showCtcAppSnackBar(
                                    context, "File uploaded successfully.");
                              } catch (e) {
                                showCtcAppSnackBar(
                                    context, "Upload failed: $e");
                              } finally {
                                setState(() => isUploadingDoc = false);
                              }
                            } else {
                              showCtcAppSnackBar(
                                  context, "File too large. Max size is 3MB.");
                            }
                          } else {
                            showCtcAppSnackBar(context, "No file selected.");
                          }
                        },
                        onView: () {
                          final url = widget.userdocId != null
                              ? usermodelw?.documents
                              : documentUrl;
                          if (url != null && url.isNotEmpty) {
                            launchUrlString(url);
                          } else {
                            showCtcAppSnackBar(context, "No document to view.");
                          }
                        },
                      ),

                      const SizedBox(height: 20),

                      Divider(),

                      const SizedBox(height: 20),

                      NomineeDetailsSection(
                          nomineeNamectrl: nomineeNamectrl,
                          nomineeRelationctrl: nomineeRelationctrl),

                      const SizedBox(height: 20),

                      LongTermLoanSection(
                        ltLoanCheck: ltLoanCheck,
                        onCheckChanged: (value) =>
                            setState(() => ltLoanCheck = value ?? false),
                        ltAppliedOnDate: ltAppliedOnDate,
                        ltApprovedOnDate: ltApprovedOnDate,
                        onAppliedOnTap: () async {
                          ltAppliedOnDate = await showDatePicker(
                                context: context,
                                initialDate: ltAppliedOnDate ?? DateTime.now(),
                                firstDate: DateTime(DateTime.now().year - 1),
                                lastDate: DateTime(DateTime.now().year + 1),
                              ) ??
                              DateTime.now();
                          setState(() {});
                        },
                        onApprovedOnTap: () async {
                          ltApprovedOnDate = await showDatePicker(
                                context: context,
                                initialDate: ltApprovedOnDate ?? DateTime.now(),
                                firstDate: DateTime(DateTime.now().year - 1),
                                lastDate: DateTime(DateTime.now().year + 1),
                              ) ??
                              DateTime.now();
                          setState(() {});
                        },
                        // Pass all controllers and file upload handlers as above
                        ltloanTypeCtrl: ltloanTypeCtrl,
                        ltloanAppNoCtrl: ltloanAppNoCtrl,
                        ltloanDesigationCtrl: ltloanDesigationCtrl,
                        ltloanReasonCtrl: ltloanReasonCtrl,
                        lloanAppliedLoanAmtCtrl: ltloanAppliedLoanAmtCtrl,
                        ltloanTotalAmtCtrl: ltloanTotalAmtCtrl,
                        ltloanAppliedLoanAmtInWordsCtrl:
                            ltloanAppliedLoanAmtInWordsCtrl,
                        ltloanMonthlyInstallmentAmtCtrl:
                            ltloanMonthlyInstallmentAmtCtrl,
                        ltloanTotalLoanPaidCtrl: ltloanTotalLoanPaidCtrl,
                        ltloanTotalInterestPaidCtrl:
                            ltloanTotalInterestPaidCtrl,
                        ltloanTotalLoanDueCtrl: ltloanTotalLoanDueCtrl,
                        ltloanShareCtrl: ltloanShareCtrl,
                        ltloanBankAccNumCtrl: ltloanBankAccNumCtrl,
                        ltloanLtSurityName1Ctrl: ltloanLtSurityName1Ctrl,
                        ltloanLtSurityName2Ctrl: ltloanLtSurityName2Ctrl,
                        ltloanLtSurityAcc1Ctrl: ltloanLtSurityAcc1Ctrl,
                        ltloanLtSurityAcc2Ctrl: ltloanLtSurityAcc2Ctrl,
                        ltloanUserSignuploadedUrl: ltloanUserSignuploadedUrl,
                        ltloanUserDocUploadedUrl: ltloanUserDocUploadedUrl,
                        isUploadingltBSign: isUploadingltBSign,
                        isUploadingltDoc: isUploadingltDoc,
                        onSignUpload: () async {
                          await pickFiles();
                          if (pdfBytes != null)
                            await uploadFile(isSignature: true, isLt: true);
                        },
                        onDocUpload: () async {
                          await pickFiles();
                          if (pdfBytes != null)
                            await uploadFile(isSignature: false, isLt: true);
                        },
                        onRemoveSign: () => setState(() {
                          ltloanUserSignuploadedUrl = null;
                          pdfBytes = null;
                          pdfFileName = null;
                          uploadTask = null;
                        }),
                        onRemoveDoc: () => setState(() {
                          ltloanUserDocUploadedUrl = null;
                          pdfBytes = null;
                          pdfFileName = null;
                          uploadTask = null;
                        }),
                        onViewSign: () async {
                          if (ltloanUserSignuploadedUrl != null) {
                            final uri = Uri.parse(ltloanUserSignuploadedUrl!);
                            if (await canLaunchUrl(uri)) {
                              await launchUrl(uri,
                                  mode: LaunchMode.externalApplication);
                            }
                          }
                        },
                        onViewDoc: () async {
                          if (ltloanUserDocUploadedUrl != null) {
                            final uri = Uri.parse(ltloanUserDocUploadedUrl!);
                            if (await canLaunchUrl(uri)) {
                              await launchUrl(uri,
                                  mode: LaunchMode.externalApplication);
                            }
                          }
                        },
                      ),

                      const SizedBox(height: 20),

                      ShortTermLoanSection(
                        stLoanCheck: stLoanCheck,
                        onCheckChanged: (value) =>
                            setState(() => stLoanCheck = value ?? false),
                        stAppliedOnDate: stAppliedOnDate,
                        stApprovedOnDate: stApprovedOnDate,
                        onAppliedOnTap: () async {
                          stAppliedOnDate = await showDatePicker(
                                context: context,
                                initialDate: stAppliedOnDate ?? DateTime.now(),
                                firstDate: DateTime(DateTime.now().year - 1),
                                lastDate: DateTime(DateTime.now().year + 1),
                              ) ??
                              DateTime.now();
                          setState(() {});
                        },
                        onApprovedOnTap: () async {
                          stApprovedOnDate = await showDatePicker(
                                context: context,
                                initialDate: stApprovedOnDate ?? DateTime.now(),
                                firstDate: DateTime(DateTime.now().year - 1),
                                lastDate: DateTime(DateTime.now().year + 1),
                              ) ??
                              DateTime.now();
                          setState(() {});
                        },
                        stloanTypeCtrl: stloanTypeCtrl,
                        stloanAppNoCtrl: stloanAppNoCtrl,
                        stloanReasonCtrl: stloanReasonCtrl,
                        stloanTotalLoanAmtCtrl: stloanTotalLoanAmtCtrl,
                        stloanAppliedLoanAmtCtrl: stloanAppliedLoanAmtCtrl,
                        stloanAppliedLoanAmtInWordsCtrl:
                            stloanAppliedLoanAmtInWordsCtrl,
                        stloanMonthlyInstallmentAmtCtrl:
                            stloanMonthlyInstallmentAmtCtrl,
                        stloanTotalLoanPaidCtrl: stloanTotalLoanPaidCtrl,
                        stloanTotalInterestPaidCtrl:
                            stloanTotalInterestPaidCtrl,
                        stloanTotalLoanDueCtrl: stloanTotalLoanDueCtrl,
                        stloanShareCtrl: stloanShareCtrl,
                        stloanBankAccNumCtrl: stloanBankAccNumCtrl,
                        stloanLtSurityName1Ctrl: stloanLtSurityName1Ctrl,
                        stloanLtSurityName2Ctrl: stloanLtSurityName2Ctrl,
                        stloanUserSignuploadedUrl: stloanUserSignuploadedUrl,
                        stloanUserDocUploadedUrl: stloanUserDocUploadedUrl,
                        isUploadingStBSign: isUploadingStBSign,
                        isUploadingStDoc: isUploadingStDoc,
                        onSignUpload: () async {
                          await pickFiles();
                          if (pdfBytes != null)
                            await uploadFile(isSignature: true, isLt: false);
                        },
                        onDocUpload: () async {
                          await pickFiles();
                          if (pdfBytes != null)
                            await uploadFile(isSignature: false, isLt: false);
                        },
                        onRemoveSign: () => setState(() {
                          stloanUserSignuploadedUrl = null;
                          pdfBytes = null;
                          pdfFileName = null;
                          uploadTask = null;
                        }),
                        onRemoveDoc: () => setState(() {
                          stloanUserDocUploadedUrl = null;
                          pdfBytes = null;
                          pdfFileName = null;
                          uploadTask = null;
                        }),
                        onViewSign: () async {
                          if (stloanUserSignuploadedUrl != null) {
                            final uri = Uri.parse(stloanUserSignuploadedUrl!);
                            if (await canLaunchUrl(uri)) {
                              await launchUrl(uri,
                                  mode: LaunchMode.externalApplication);
                            }
                          }
                        },
                        onViewDoc: () async {
                          if (stloanUserDocUploadedUrl != null) {
                            final uri = Uri.parse(stloanUserDocUploadedUrl!);
                            if (await canLaunchUrl(uri)) {
                              await launchUrl(uri,
                                  mode: LaunchMode.externalApplication);
                            }
                          }
                        },
                      ),

                      const SizedBox(height: 30),

                      SubsDetailsSection(
                        totalsubsctrl: totalsubsctrl,
                        // totalSubsIntctrl: totalSubsIntctrl,
                      ),

                      const SizedBox(height: 20),

                      StLoanDetailsSection(
                        totalStLoansctrl: totalStLoansctrl,
                        stLoansDuectrl: stLoansDuectrl,
                        totalStIntPaidctrl: totalStIntPaidctrl,
                      ),

                      const SizedBox(height: 20),

                      LtLoanDetailsSection(
                        totalLtLoansctrl: totalLtLoansctrl,
                        ltLoansDuectrl: ltLoansDuectrl,
                        totalLtIntPaidctrl: totalLtIntPaidctrl,
                        // ltAmtPaidTillNowCtrl: ltAmtPaidTillNowCtrl,
                        // ltEmiPaidTillNowMonthsCtrl: ltEmiPaidTillNowMonthsCtrl,
                      ),

                      const SizedBox(height: 20),

                      LastMonthRecoveryDetailsSection(
                        selectedMonth: selectedMonth,
                        selectedYear: selectedYear,
                        yearList: yearList,
                        month: month,
                        onMonthChanged: (value) =>
                            setState(() => selectedMonth = value),
                        onYearChanged: (value) =>
                            setState(() => selectedYear = value),
                        userPrevObltCtrl: userPrevObltCtrl,
                        userPrevObstCtrl: userPrevObstCtrl,
                        userPrevLtLoanpaidCtrl: userPrevLtLoanpaidCtrl,
                        userPrevStLoanpaidCtrl: userPrevStLoanpaidCtrl,
                        userPrevTotalLoanCtrl: userPrevTotalLoanCtrl,
                        userPrevSubsCtrl: userPrevSubsCtrl,
                        userPrevLtInstallmentCtrl: userPrevLtInstallmentCtrl,
                        userPrevStInstallmentCtrl: userPrevStInstallmentCtrl,
                        userPrevInterestCtrl: userPrevInterestCtrl,
                        duesCtrl: duesCtrl,
                        penaltyCtrl: penaltyCtrl,
                        userPrevLoanTotalSecondCtrl:
                            userPrevLoanTotalSecondCtrl,
                        userPrevCbltCtrl: userPrevCbltCtrl,
                        userPrevCbstCtrl: userPrevCbstCtrl,
                        userPrevInstallmentReceivedCtrl:
                            userPrevInstallmentReceivedCtrl,
                        installmentRecDate: installmentRecDate,
                        onInstallmentRecDateTap: () async {
                          installmentRecDate = await showDatePicker(
                                context: context,
                                initialDate:
                                    installmentRecDate ?? DateTime.now(),
                                firstDate: DateTime(DateTime.now().year - 1),
                                lastDate: DateTime(DateTime.now().year + 1),
                              ) ??
                              DateTime.now();
                          setState(() {});
                        },
                        userPrevSubsPaidCtrl: userPrevSubsPaidCtrl,
                        userPrevLtInterestpaidCtrl: userPrevLtInterestpaidCtrl,
                        userPrevStInterestpaidCtrl: userPrevStInterestpaidCtrl,
                        userPrevLtInstallmentpaidCtrl:
                            userPrevLtInstallmentpaidCtrl,
                        userPrevStInstallmentpaidCtrl:
                            userPrevStInstallmentpaidCtrl,
                        userPrevPenaltyPaidCtrl: userPrevPenaltyPaidCtrl,
                      ),
                    ],
                  ),
                ),
                //submit button
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 25, vertical: 20),
                  width: double.infinity,
                  // color: Colors.grey[
                  //     200], // or use Theme.of(context).appBarTheme.backgroundColor
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      addUserSubmitFormButton(context, ctrl),
                    ],
                  ),
                )
              ],
            );
    });
  }

  CustomHeaderButton addUserSubmitFormButton(
      BuildContext context, HomeCtrl ctrl) {
    return CustomHeaderButton(
      buttonName: 'Submit Form',
      onPressed: () async {
        if (cpfnoctrl.text.trim().isEmpty) {
          showCtcAppSnackBar(context, "Enter CPF Number");
          return;
        }
        if (employeenoctrl.text.trim().isEmpty) {
          showCtcAppSnackBar(context, "Enter Employee Number");
          return;
        }

        int countCpf = (await FBFireStore.users
                    .where('cpfNo', isEqualTo: num.tryParse(cpfnoctrl.text))
                    .count()
                    .get())
                .count ??
            0;
        int countEmp = (await FBFireStore.users
                    .where('employeeNo',
                        isEqualTo: num.tryParse(employeenoctrl.text))
                    .count()
                    .get())
                .count ??
            0;

        if (widget.userdocId == null) {
          if (countCpf > 0) {
            showCtcAppSnackBar(context, "Cpf Number already exists!");
            return;
          }
          if (countEmp > 0) {
            showCtcAppSnackBar(context, "Employee Number already exists!");
            return;
          }
        } else {
          if (countCpf == 0) {
            showCtcAppSnackBar(context, "Invalid Cpf Number");
            return;
          }
          if (countEmp == 0) {
            showCtcAppSnackBar(context, "Invalid Employee Number");
            return;
          }
        }

        QuerySnapshot<Map<String, dynamic>> existingemail = await FBFireStore
            .users
            .where('email', isEqualTo: emailctrl.text.trim().toLowerCase())
            .get();

        if (existingemail.docs.isNotEmpty) {
          showCtcAppSnackBar(context, 'Email already exist');
          return;
        }

        if (nctrl.text.isEmpty ||
                // cpfnoctrl.text.isEmpty ||
                selectedofficeid == null ||
                emailctrl.text.isEmpty ||
                phonectrl.text.isEmpty ||
                addressctrl.text.isEmpty ||
                paddressctrl.text.isEmpty
            // ||
            // employeenoctrl.text.isEmpty
            ) {
          return showCtcAppSnackBar(context, "Empty Fields ! ");
        } else if (phonectrl.text.length != 10) {
          return showCtcAppSnackBar(context, "Invalid Phone Number!!");
        } else if (!isValidEmail(emailctrl.text.trim())) {
          return showCtcAppSnackBar(context, "Enter Valid Email");
        } else if (documentUrl == null || documentUrl!.isEmpty) {
          return showCtcAppSnackBar(context, "Please upload a document.");
        }
        // else if (bankacnamectrl.text.isEmpty ||
        //     banknamectrl.text.isEmpty ||
        //     bankacnoctrl.text.isEmpty ||
        //     ifsccodectrl.text.isEmpty) {
        //   return showCtcAppSnackBar(context, "Please fill all bank details.");
        // }
        else if (totalSharesctrl.text.isEmpty) {
          return showCtcAppSnackBar(
              context, "Please enter Total Shares Value.");
        } else {
          setState(() {
            isLoading = true;
          });
          // documentUrl = await uploadFile();
          try {
            final data = <String, dynamic>{
              'name':
                  nctrl.text.isNotEmpty ? nctrl.text.toUpperCase() : 'Unknown',
              'cpfNo': cpfnoctrl.text.isNotEmpty
                  ? num.tryParse(cpfnoctrl.text)
                  : 'Unknown',
              'employeeNo': employeenoctrl.text.isNotEmpty
                  ? num.tryParse(employeenoctrl.text)
                  : '-',
              'currentAddress':
                  addressctrl.text.isNotEmpty ? addressctrl.text : '-',
              'permanentAddress':
                  paddressctrl.text.isNotEmpty ? paddressctrl.text : '-',
              'districtoffice': selectedofficeid ?? '',
              'email': emailctrl.text.isNotEmpty
                  ? emailctrl.text.toLowerCase()
                  : '<EMAIL>',
              'phonenumber':
                  phonectrl.text.isNotEmpty ? phonectrl.text : '0000000000',
              // 'createdAt': DateTime.now(),
              'settlement': num.tryParse(settlementctrl.text) ?? 0,
              'totalSubs': num.tryParse(totalsubsctrl.text) ?? 0,
              'totalSubsInt': num.tryParse(totalSubsIntctrl.text) ?? 0,
              'ltLoansDue': num.tryParse(ltLoansDuectrl.text) ?? 0,
              'stLoansDue': num.tryParse(stLoansDuectrl.text) ?? 0,
              'totalLtLoans': num.tryParse(totalLtLoansctrl.text) ?? 0,
              'totalStLoans': num.tryParse(totalStLoansctrl.text) ?? 0,
              'totalLtIntPaid': num.tryParse(totalLtIntPaidctrl.text) ?? 0,
              'totalStIntPaid': num.tryParse(totalStIntPaidctrl.text) ?? 0,
              'totalDivident': num.tryParse(totalDividentctrl.text) ?? 0,
              'totalShares': num.tryParse(totalSharesctrl.text) ?? 0,
              'password': generateRandomId(8),
              'documents': documentUrl ?? "",
              'approved': true,
              'bankAcName': bankacnamectrl.text,
              'bankName': banknamectrl.text,
              'ifscCode': ifsccodectrl.text,
              'bankAcNo': num.tryParse(bankacnoctrl.text),
              'archived': false,
              'nomineeName': nomineeNamectrl.text,
              'nomineeRelation': nomineeRelationctrl.text,
              'momento': null,
              'userPrevoiusMonthlyRecord': {
                'selectedyear': selectedYear,
                'selectedmonth': selectedMonth,
                'districtoffice': selectedofficeid ?? '',
                'cpfNo': cpfnoctrl.text.isNotEmpty
                    ? num.tryParse(cpfnoctrl.text)
                    : '',
                'name': nctrl.text.isNotEmpty ? nctrl.text.toUpperCase() : '',
                'obLt': num.tryParse(userPrevObltCtrl.text) ?? 0,
                'obSt': num.tryParse(userPrevObstCtrl.text) ?? 0,
                'loanPaidLt': num.tryParse(userPrevLtLoanpaidCtrl.text) ?? 0,
                'loanPaidst': num.tryParse(userPrevStLoanpaidCtrl.text) ?? 0,
                'loanTotal': num.tryParse(userPrevTotalLoanCtrl.text) ?? 0,
                'subs': num.tryParse(userPrevSubsCtrl.text) ?? 0,
                'ltInstallment':
                    num.tryParse(userPrevLtInstallmentCtrl.text) ?? 0,
                'stInstallment':
                    num.tryParse(userPrevStInstallmentCtrl.text) ?? 0,
                'interest': num.tryParse(userPrevInterestCtrl.text) ?? 0,
                'total': num.tryParse(userPrevLoanTotalSecondCtrl.text) ?? 0,
                'installmentRec':
                    num.tryParse(userPrevInstallmentReceivedCtrl.text) ?? 0,
                'installmentRecDate': installmentRecDate != null
                    ? Timestamp.fromDate(installmentRecDate!)
                    : null,
                'ltCb': num.tryParse(userPrevCbltCtrl.text) ?? 0,
                'stCb': num.tryParse(userPrevCbstCtrl.text) ?? 0,
                'subscriptionPaid':
                    num.tryParse(userPrevSubsPaidCtrl.text) ?? 0,
                'longTermInstalmentPaid':
                    num.tryParse(userPrevLtInstallmentpaidCtrl.text) ?? 0,
                'shortTermInstalmentPaid':
                    num.tryParse(userPrevStInstallmentpaidCtrl.text) ?? 0,
                'longTermInterestPaid':
                    num.tryParse(userPrevLtInterestpaidCtrl.text) ?? 0,
                'shortTermInterestPaid':
                    num.tryParse(userPrevStInterestpaidCtrl.text) ?? 0,
                'penaltyPaid': num.tryParse(userPrevPenaltyPaidCtrl.text) ?? 0,
                'totalReceived': 0,
                'isPaid': (num.tryParse(userPrevLoanTotalSecondCtrl.text) ??
                        0) ==
                    (num.tryParse(userPrevInstallmentReceivedCtrl.text) ?? 0),
                'status': (num.tryParse(userPrevLoanTotalSecondCtrl.text) ??
                            0) ==
                        ((num.tryParse(userPrevInstallmentReceivedCtrl.text) ??
                            0))
                    ? 'Paid'
                    : ((((num.tryParse(userPrevInstallmentReceivedCtrl.text) ??
                                0))) >
                            0
                        ? 'Partially Paid'
                        : 'Unpaid'),
              },
            };

            // print("data : $data");

            if (usermodelw == null) {
              final functionName = testMode ? 'testCreateUser' : 'createUser';
              final result =
                  await FBFunctions.ff.httpsCallable(functionName).call(data);
              final resultData = Map<String, dynamic>.from(result.data);

              if (resultData['success'] != true || resultData['uid'] == null) {
                if (resultData['code'] == 'user-already-exists') {
                  showCtcAppSnackBar(context, "Email already exists!");
                } else {
                  final errorMsg =
                      resultData['msg'] ?? "Failed to create user.";
                  showCtcAppSnackBar(
                      context, "User creation failed: $errorMsg");
                }
                return;
              }

              // print('ress $result');
              // print('ress ${result.data['msg']}');
              final String? newUserId = result.data['uid'];

              // showCtcAppSnackBar(context, "User Successfully Created.");

              // success = result.data['success'] == true;

              if (result.data['code'] == 'user-already-exists') {
                showCtcAppSnackBar(context, "Email already exist!");
              } else {
                showCtcAppSnackBar(context, "User Successfully Created.");
              }
              //loan data uploading
              if (ltLoanCheck) {
                if (pdfBytes != null) {
                  String ltSignUrl =
                      await uploadFile(isLt: true, isSignature: true);
                  String ltDocUrl =
                      await uploadFile(isLt: true, isSignature: false);
                  ltloanUserSignuploadedUrl = ltSignUrl;
                  ltloanUserDocUploadedUrl = ltDocUrl;
                }
                FBFireStore.loan.add({
                  'uid': newUserId,
                  'createdAt': ltAppliedOnDate,
                  'totalLoanAmt':
                      num.tryParse(ltloanAppliedLoanAmtCtrl.text) ?? 0,
                  'appliedLoanAmt': num.tryParse(ltloanTotalAmtCtrl.text) ?? 0,
                  'totalLoanPaid':
                      num.tryParse(ltloanTotalLoanPaidCtrl.text) ?? 0,
                  'totalLoanDue':
                      num.tryParse(ltloanTotalLoanDueCtrl.text) ?? 0,
                  'loanType': ltloanTypeCtrl.text.toString(),
                  'isSettled': false,
                  'settledOn': null, // Nullable
                  'isNew': false,
                  'appliedOn': ltAppliedOnDate,
                  'applicationNo': num.tryParse(ltloanAppNoCtrl.text) ?? 0,
                  'approvedOn': ltApprovedOnDate, // Nullable
                  'processedOn': null, // Nullable
                  'share': num.tryParse(ltloanShareCtrl.text) ?? 0,
                  'totalInterestPaid':
                      num.tryParse(ltloanTotalInterestPaidCtrl.text) ?? 0,

                  'designation': ltloanDesigationCtrl.text.toString(),
                  'bAcNo': num.tryParse(ltloanBankAccNumCtrl.text) ?? 0,
                  'appliedLoanAmtinWords':
                      ltloanAppliedLoanAmtInWordsCtrl.text.toString(),
                  'loanReason': ltloanReasonCtrl.text.toString(),
                  'bSign': ltloanUserSignuploadedUrl,
                  'surityName1': ltloanLtSurityName1Ctrl.text.toString(),
                  'surityName2': ltloanLtSurityName2Ctrl.text.toString(),
                  'sAcNo1': num.tryParse(ltloanLtSurityAcc1Ctrl.text) ?? 0,
                  'sAcNo2': num.tryParse(ltloanLtSurityAcc2Ctrl.text) ?? 0,
                  // 'balance': null,
                  'rejectionDate': null,
                  'rejectionReason': null,
                  'monthlyInstallmentAmt':
                      num.tryParse(ltloanMonthlyInstallmentAmtCtrl.text) ?? 0,
                  'loanPreClosureReason': null,
                  'pdfString': ltloanUserDocUploadedUrl,
                });
              }
              if (stLoanCheck) {
                if (pdfBytes != null) {
                  String stSignUrl =
                      await uploadFile(isLt: false, isSignature: true);
                  String stDocUrl =
                      await uploadFile(isLt: false, isSignature: false);
                  stloanUserSignuploadedUrl = stSignUrl;
                  stloanUserDocUploadedUrl = stDocUrl;
                }

                FBFireStore.loan.add({
                  'uid': newUserId,
                  'createdAt': stAppliedOnDate,
                  'totalLoanAmt':
                      num.tryParse(stloanTotalLoanAmtCtrl.text) ?? 0,
                  'appliedLoanAmt':
                      num.tryParse(stloanAppliedLoanAmtCtrl.text) ?? 0,
                  'totalLoanPaid':
                      num.tryParse(stloanTotalLoanPaidCtrl.text) ?? 0,
                  'totalLoanDue':
                      num.tryParse(stloanTotalLoanDueCtrl.text) ?? 0,
                  'loanType': stloanTypeCtrl.text.toString(),
                  'isSettled': false,
                  'settledOn': null, // Nullable
                  'isNew': false,
                  'appliedOn': stAppliedOnDate,
                  'applicationNo': num.tryParse(stloanAppNoCtrl.text) ?? 0,
                  'approvedOn': stApprovedOnDate, // Nullable
                  'processedOn': null, // Nullable
                  'share': num.tryParse(stloanShareCtrl.text) ?? 0,
                  'totalInterestPaid':
                      num.tryParse(stloanTotalInterestPaidCtrl.text) ?? 0,

                  'designation': null,
                  'bAcNo': num.tryParse(stloanBankAccNumCtrl.text) ?? 0,
                  'appliedLoanAmtinWords':
                      stloanAppliedLoanAmtInWordsCtrl.text.toString(),
                  'loanReason': stloanReasonCtrl.text.toString(),
                  'bSign': stloanUserSignuploadedUrl,
                  'surityName1': stloanLtSurityName1Ctrl.text,
                  'surityName2': stloanLtSurityName2Ctrl.text,
                  'sAcNo1': null,
                  'sAcNo2': null,
                  'rejectionDate': null,
                  'rejectionReason': null,
                  'monthlyInstallmentAmt':
                      num.tryParse(stloanMonthlyInstallmentAmtCtrl.text) ?? 0,
                  'loanPreClosureReason': null,
                  'pdfString': stloanUserDocUploadedUrl,
                });
              }

              if (ltLoanCheck || stLoanCheck) {
                fbUserRecoveryMonthlyDataFetch = await FBFireStore
                    .recoverymonthly
                    .where('selectedyear', isEqualTo: selectedYear)
                    .where('selectedmonth', isEqualTo: selectedMonth)
                    .where('doId', isEqualTo: selectedofficeid)
                    .get();

                // print(
                //     "fbUserRecoveryMonthlyDataFetch : ${fbUserRecoveryMonthlyDataFetch?.docs.length}");

                final fbUsersMonthlyDataFetch = await FBFireStore.usermonthly
                    .where('selectedyear', isEqualTo: selectedYear)
                    .where('selectedmonth', isEqualTo: selectedMonth)
                    .where('districtoffice', isEqualTo: selectedofficeid)
                    .where('cpfNo',
                        isEqualTo: num.tryParse(cpfnoctrl.text) ?? 0)
                    .get();

                // print(
                //     "fbUsersMonthlyDataFetch : ${fbUsersMonthlyDataFetch.docs.length}");

                if (fbUserRecoveryMonthlyDataFetch?.size == 0) {
                  FBFireStore.recoverymonthly.add({
                    'obLt': num.tryParse(userPrevObltCtrl.text) ?? 0,
                    'obSt': num.tryParse(userPrevObstCtrl.text) ?? 0,
                    'loanPaidLt':
                        num.tryParse(userPrevLtLoanpaidCtrl.text) ?? 0,
                    'loanPaidst':
                        num.tryParse(userPrevStLoanpaidCtrl.text) ?? 0,
                    'loanTotal': num.tryParse(userPrevTotalLoanCtrl.text) ?? 0,
                    'subs': num.tryParse(userPrevSubsCtrl.text) ?? 0,
                    'ltInstallment':
                        num.tryParse(userPrevLtInstallmentCtrl.text) ?? 0,
                    'stInstallment':
                        num.tryParse(userPrevStInstallmentCtrl.text) ?? 0,
                    'interest': num.tryParse(userPrevInterestCtrl.text) ?? 0,
                    'total':
                        num.tryParse(userPrevLoanTotalSecondCtrl.text) ?? 0,
                    'installmentRec':
                        num.tryParse(userPrevInstallmentReceivedCtrl.text) ?? 0,
                    'installmentRecDate': installmentRecDate != null
                        ? Timestamp.fromDate(installmentRecDate!)
                        : null,
                    'ltCb': num.tryParse(userPrevCbltCtrl.text) ?? 0,
                    'stCb': num.tryParse(userPrevCbstCtrl.text) ?? 0,
                    'subscriptionPaid':
                        num.tryParse(userPrevSubsPaidCtrl.text) ?? 0,
                    'longTermInstalmentPaid':
                        num.tryParse(userPrevLtInstallmentpaidCtrl.text) ?? 0,
                    'shortTermInstalmentPaid':
                        num.tryParse(userPrevStInstallmentpaidCtrl.text) ?? 0,
                    'longTermInterestPaid':
                        num.tryParse(userPrevLtInterestpaidCtrl.text) ?? 0,
                    'shortTermInterestPaid':
                        num.tryParse(userPrevStInterestpaidCtrl.text) ?? 0,
                    'totalReceived': 0,
                    'selectedyear': selectedYear,
                    'selectedmonth': selectedMonth,
                    'doId': selectedofficeid ?? '',
                  });
                } else {
                  final existingData =
                      fbUserRecoveryMonthlyDataFetch!.docs.first.data();

                  num fbDataValue(String key) => existingData.containsKey(key)
                      ? (existingData[key] ?? 0)
                      : 0;

                  FBFireStore.recoverymonthly
                      .doc(fbUserRecoveryMonthlyDataFetch?.docs.first.id)
                      .update({
                    'obLt': fbDataValue('obLt') +
                        (num.tryParse(userPrevObltCtrl.text) ?? 0),
                    'obSt': fbDataValue('obSt') +
                        (num.tryParse(userPrevObstCtrl.text) ?? 0),
                    'loanPaidLt': fbDataValue('loanPaidLt') +
                        (num.tryParse(userPrevLtLoanpaidCtrl.text) ?? 0),
                    'loanPaidst': fbDataValue('loanPaidst') +
                        (num.tryParse(userPrevStLoanpaidCtrl.text) ?? 0),
                    'loanTotal': fbDataValue('loanTotal') +
                        (num.tryParse(userPrevTotalLoanCtrl.text) ?? 0),
                    'subs': fbDataValue('subs') +
                        (num.tryParse(userPrevSubsCtrl.text) ?? 0),
                    'ltInstallment': fbDataValue('ltInstallment') +
                        (num.tryParse(userPrevLtInstallmentCtrl.text) ?? 0),
                    'stInstallment': fbDataValue('stInstallment') +
                        (num.tryParse(userPrevStInstallmentCtrl.text) ?? 0),
                    'interest': fbDataValue('interest') +
                        (num.tryParse(userPrevInterestCtrl.text) ?? 0),
                    'total': fbDataValue('total') +
                        (num.tryParse(userPrevLoanTotalSecondCtrl.text) ?? 0),
                    'installmentRec': fbDataValue('installmentRec') +
                        (num.tryParse(userPrevInstallmentReceivedCtrl.text) ??
                            0),
                    'installmentRecDate': installmentRecDate != null
                        ? Timestamp.fromDate(installmentRecDate!)
                        : null,
                    'ltCb': fbDataValue('ltCb') +
                        (num.tryParse(userPrevCbltCtrl.text) ?? 0),
                    'stCb': fbDataValue('stCb') +
                        (num.tryParse(userPrevCbstCtrl.text) ?? 0),
                    'subscriptionPaid': fbDataValue('subscriptionPaid') +
                        (num.tryParse(userPrevSubsPaidCtrl.text) ?? 0),
                    'longTermInstalmentPaid':
                        fbDataValue('longTermInstalmentPaid') +
                            (num.tryParse(userPrevLtInstallmentpaidCtrl.text) ??
                                0),
                    'shortTermInstalmentPaid':
                        fbDataValue('shortTermInstalmentPaid') +
                            (num.tryParse(userPrevStInstallmentpaidCtrl.text) ??
                                0),
                    'longTermInterestPaid':
                        fbDataValue('longTermInterestPaid') +
                            (num.tryParse(userPrevLtInterestpaidCtrl.text) ??
                                0),
                    'shortTermInterestPaid':
                        fbDataValue('shortTermInterestPaid') +
                            (num.tryParse(userPrevStInterestpaidCtrl.text) ??
                                0),
                    'totalReceived': 0,
                    'selectedyear': selectedYear,
                    'selectedmonth': selectedMonth,
                    'doId': selectedofficeid ?? "",
                  });
                }

                if (fbUsersMonthlyDataFetch.size == 0) {
                  FBFireStore.usermonthly.add({
                    'selectedyear': selectedYear,
                    'selectedmonth': selectedMonth,
                    'districtoffice': selectedofficeid ?? '',
                    'cpfNo': num.tryParse(cpfnoctrl.text) ?? 0,
                    'name': nctrl.text.isNotEmpty
                        ? nctrl.text.toUpperCase()
                        : 'Unknown',
                    'obLt': num.tryParse(userPrevObltCtrl.text) ?? 0,
                    'obSt': num.tryParse(userPrevObstCtrl.text) ?? 0,
                    'loanPaidLt':
                        num.tryParse(userPrevLtLoanpaidCtrl.text) ?? 0,
                    'loanPaidst':
                        num.tryParse(userPrevStLoanpaidCtrl.text) ?? 0,
                    'loanTotal': num.tryParse(userPrevTotalLoanCtrl.text) ?? 0,
                    'subs': num.tryParse(userPrevSubsCtrl.text) ?? 0,
                    'ltInstallment':
                        num.tryParse(userPrevLtInstallmentCtrl.text) ?? 0,
                    'stInstallment':
                        num.tryParse(userPrevStInstallmentCtrl.text) ?? 0,
                    'interest': num.tryParse(userPrevInterestCtrl.text) ?? 0,
                    'total':
                        num.tryParse(userPrevLoanTotalSecondCtrl.text) ?? 0,
                    'installmentRec':
                        num.tryParse(userPrevInstallmentReceivedCtrl.text) ?? 0,
                    'installmentRecDate': installmentRecDate != null
                        ? Timestamp.fromDate(installmentRecDate!)
                        : null,
                    'ltCb': num.tryParse(userPrevCbltCtrl.text) ?? 0,
                    'stCb': num.tryParse(userPrevCbstCtrl.text) ?? 0,
                    'subscriptionPaid':
                        num.tryParse(userPrevSubsPaidCtrl.text) ?? 0,
                    'longTermInstalmentPaid':
                        num.tryParse(userPrevLtInstallmentpaidCtrl.text) ?? 0,
                    'shortTermInstalmentPaid':
                        num.tryParse(userPrevStInstallmentpaidCtrl.text) ?? 0,
                    'longTermInterestPaid':
                        num.tryParse(userPrevLtInterestpaidCtrl.text) ?? 0,
                    'shortTermInterestPaid':
                        num.tryParse(userPrevStInterestpaidCtrl.text) ?? 0,
                    'penaltyPaid':
                        num.tryParse(userPrevPenaltyPaidCtrl.text) ?? 0,
                    'totalReceived': 0,
                    'isPaid': false,
                    'status': ((num.tryParse(
                                    userPrevLoanTotalSecondCtrl.text) ??
                                0) ==
                            (num.tryParse(
                                    userPrevInstallmentReceivedCtrl.text) ??
                                0))
                        ? 'Paid'
                        : ((num.tryParse(
                                        userPrevInstallmentReceivedCtrl.text) ??
                                    0) >
                                0
                            ? 'Partially Paid'
                            : 'Unpaid')
                  });
                } else {
                  final userExistingData =
                      fbUsersMonthlyDataFetch.docs.first.data();

                  num fbUserMonthlyDataValue(String key) =>
                      userExistingData.containsKey(key)
                          ? (userExistingData[key] ?? 0)
                          : 0;

                  FBFireStore.usermonthly
                      .doc(fbUserMonthlyDataFetch?.docs.first.id)
                      .update({
                    'selectedyear': selectedYear,
                    'selectedmonth': selectedMonth,
                    'districtoffice': selectedofficeid ?? '',
                    'cpfNo': num.tryParse(cpfnoctrl.text) ?? 0,
                    'name': nctrl.text,
                    'obLt': fbUserMonthlyDataValue('obLt') +
                        (num.tryParse(userPrevObltCtrl.text) ?? 0),
                    'obSt': fbUserMonthlyDataValue('obSt') +
                        (num.tryParse(userPrevObstCtrl.text) ?? 0),
                    'loanPaidLt': fbUserMonthlyDataValue('loanPaidLt') +
                        (num.tryParse(userPrevLtLoanpaidCtrl.text) ?? 0),
                    'loanPaidst': fbUserMonthlyDataValue('loanPaidst') +
                        (num.tryParse(userPrevStLoanpaidCtrl.text) ?? 0),
                    'loanTotal': fbUserMonthlyDataValue('loanTotal') +
                        (num.tryParse(userPrevTotalLoanCtrl.text) ?? 0),
                    'subs': fbUserMonthlyDataValue('subs') +
                        (num.tryParse(userPrevSubsCtrl.text) ?? 0),
                    'ltInstallment': fbUserMonthlyDataValue('ltInstallment') +
                        (num.tryParse(userPrevLtInstallmentCtrl.text) ?? 0),
                    'stInstallment': fbUserMonthlyDataValue('stInstallment') +
                        (num.tryParse(userPrevStInstallmentCtrl.text) ?? 0),
                    'interest': fbUserMonthlyDataValue('interest') +
                        (num.tryParse(userPrevInterestCtrl.text) ?? 0),
                    'total': fbUserMonthlyDataValue('total') +
                        (num.tryParse(userPrevLoanTotalSecondCtrl.text) ?? 0),
                    'installmentRec': fbUserMonthlyDataValue('installmentRec') +
                        (num.tryParse(userPrevInstallmentReceivedCtrl.text) ??
                            0),
                    'installmentRecDate': installmentRecDate != null
                        ? Timestamp.fromDate(installmentRecDate!)
                        : null,
                    'ltCb': fbUserMonthlyDataValue('ltCb') +
                        (num.tryParse(userPrevCbltCtrl.text) ?? 0),
                    'stCb': fbUserMonthlyDataValue('stCb') +
                        (num.tryParse(userPrevCbstCtrl.text) ?? 0),
                    'subscriptionPaid':
                        fbUserMonthlyDataValue('subscriptionPaid') +
                            (num.tryParse(userPrevSubsPaidCtrl.text) ?? 0),
                    'longTermInstalmentPaid':
                        fbUserMonthlyDataValue('longTermInstalmentPaid') +
                            (num.tryParse(userPrevLtInstallmentpaidCtrl.text) ??
                                0),
                    'shortTermInstalmentPaid':
                        fbUserMonthlyDataValue('shortTermInstalmentPaid') +
                            (num.tryParse(userPrevStInstallmentpaidCtrl.text) ??
                                0),
                    'longTermInterestPaid':
                        fbUserMonthlyDataValue('longTermInterestPaid') +
                            (num.tryParse(userPrevLtInterestpaidCtrl.text) ??
                                0),
                    'shortTermInterestPaid':
                        fbUserMonthlyDataValue('shortTermInterestPaid') +
                            (num.tryParse(userPrevStInterestpaidCtrl.text) ??
                                0),
                    'penaltyPaid': fbUserMonthlyDataValue('penaltyPaid') +
                        (num.tryParse(userPrevPenaltyPaidCtrl.text) ?? 0),
                    'totalReceived': 0,
                    'isPaid': false,
                    'status': ((num.tryParse(
                                    userPrevLoanTotalSecondCtrl.text) ??
                                0) ==
                            (num.tryParse(
                                    userPrevInstallmentReceivedCtrl.text) ??
                                0))
                        ? 'Paid'
                        : ((num.tryParse(
                                        userPrevInstallmentReceivedCtrl.text) ??
                                    0) >
                                0
                            ? 'Partially Paid'
                            : 'Unpaid')
                  });
                }

                final fbSocietyYearlyDataFetch = await FBFireStore.societyYearly
                    .where('selectedyear', isEqualTo: selectedYear)
                    .get();

                // print(
                //     "fbSocietyYearlyDataFetch : ${fbSocietyYearlyDataFetch.docs.length}");

                if (fbSocietyYearlyDataFetch.size == 0) {
                  FBFireStore.societyYearly.add({
                    'doId': selectedofficeid,
                    'createdAt': Timestamp.now(),
                    'selectedyear': selectedYear,
                    'updatedAt': null,
                    'OB': (num.tryParse(userPrevObltCtrl.text) ?? 0) +
                        (num.tryParse(userPrevObstCtrl.text) ?? 0),
                    'CB': (num.tryParse(userPrevCbltCtrl.text) ?? 0) +
                        (num.tryParse(userPrevCbstCtrl.text) ?? 0),
                    'totalSubscription': num.tryParse(totalsubsctrl.text) ?? 0,
                    'intOnSubscription':
                        num.tryParse(totalSubsIntctrl.text) ?? 0,
                    'subscriptionInterestRate': num.tryParse(
                        ctrl.settings?.subscriptionInterest ?? "0"),
                    'totalLoanGiven':
                        (num.tryParse(ltloanTotalLoanPaidCtrl.text) ?? 0) +
                            (num.tryParse(stloanTotalLoanPaidCtrl.text) ?? 0),
                    'totalLoanReceived':
                        (num.tryParse(ltloanTotalLoanPaidCtrl.text) ?? 0) +
                            (num.tryParse(stloanTotalLoanPaidCtrl.text) ?? 0),
                    'ltLoanReceived':
                        (num.tryParse(ltloanTotalLoanPaidCtrl.text) ?? 0),
                    'stLoanReceived':
                        (num.tryParse(stloanTotalLoanPaidCtrl.text) ?? 0),
                    'ltLoanGiven':
                        (num.tryParse(userPrevLtInstallmentCtrl.text) ?? 0),
                    'stLoanGiven':
                        (num.tryParse(userPrevStInstallmentCtrl.text) ?? 0),
                    'ltIntAmt':
                        (num.tryParse(userPrevLtInterestpaidCtrl.text) ?? 0),
                    'stIntAmt':
                        (num.tryParse(userPrevStInterestpaidCtrl.text) ?? 0),
                    'totalIntAmt':
                        (num.tryParse(userPrevLtInterestpaidCtrl.text) ?? 0) +
                            (num.tryParse(userPrevStInterestpaidCtrl.text) ??
                                0),
                    'loanIntRate':
                        num.tryParse(ctrl.settings!.ltloanInterest.toString()),
                    'totalPendingLoan': ((num.tryParse(ltLoansDuectrl.text) ??
                                0) +
                            (num.tryParse(stLoansDuectrl.text) ?? 0)) -
                        (num.tryParse(userPrevLtInstallmentpaidCtrl.text) ??
                            0) -
                        (num.tryParse(userPrevStInstallmentpaidCtrl.text) ?? 0),
                    'ltPendingLoan':
                        (num.tryParse(userPrevObltCtrl.text) ?? 0) -
                            (num.tryParse(userPrevLtLoanpaidCtrl.text) ?? 0),
                    'stPendingLoan':
                        (num.tryParse(userPrevObstCtrl.text) ?? 0) -
                            (num.tryParse(userPrevStLoanpaidCtrl.text) ?? 0),
                    'totalExpenses': 0,
                    'expensesIds': [],
                    'totalDividend': (num.tryParse(totalsubsctrl.text) ?? 0) *
                        (num.tryParse(ctrl.settings?.dividentRate.toString() ??
                                "0") ??
                            0),
                    'totalMonthlyShareGiven':
                        (num.tryParse(totalSharesctrl.text) ?? 0),
                    'totalShareGiven':
                        (num.tryParse(totalSharesctrl.text) ?? 0),
                    'monthlyDividend': (num.tryParse(totalsubsctrl.text) ?? 0) *
                        (num.tryParse(ctrl.settings?.dividentRate ?? "0") ?? 0),
                    'dividendRate':
                        num.tryParse(ctrl.settings?.dividentRate ?? "0"),
                    'cashBalance': num.tryParse(0.toString()),
                  });
                } else {
                  final societyYearlyExistingData =
                      fbSocietyYearlyDataFetch.docs.first.data();

                  num fbSocietyYearlyDataValue(String key) =>
                      societyYearlyExistingData.containsKey(key)
                          ? (societyYearlyExistingData[key] ?? 0)
                          : 0;
                  FBFireStore.societyYearly
                      .doc(fbSocietyYearlyDataFetch.docs.first.id)
                      .update({
                    'doId': selectedofficeid,
                    'createdAt': fbSocietyYearlyDataValue('createdAt'),
                    'selectedyear': selectedYear,
                    'updatedAt': Timestamp.now(),
                    'OB': fbSocietyYearlyDataValue('OB') +
                        (num.tryParse(userPrevObltCtrl.text) ?? 0) +
                        (num.tryParse(userPrevObstCtrl.text) ?? 0),
                    'CB': fbSocietyYearlyDataValue('CB') +
                        (num.tryParse(userPrevCbltCtrl.text) ?? 0) +
                        (num.tryParse(userPrevCbstCtrl.text) ?? 0),
                    'totalSubscription':
                        fbSocietyYearlyDataValue('totalSubscription') +
                            (num.tryParse(totalsubsctrl.text) ?? 0),
                    'intOnSubscription':
                        fbSocietyYearlyDataValue('intOnSubscription') +
                            (num.tryParse(totalSubsIntctrl.text) ?? 0),
                    'subscriptionInterestRate': num.tryParse(
                        ctrl.settings?.subscriptionInterest ?? "0"),
                    'totalLoanGiven':
                        fbSocietyYearlyDataValue('totalLoanGiven') +
                            (num.tryParse(ltloanTotalLoanPaidCtrl.text) ?? 0) +
                            (num.tryParse(stloanTotalLoanPaidCtrl.text) ?? 0),
                    'totalLoanReceived':
                        fbSocietyYearlyDataValue('totalLoanReceived') +
                            (num.tryParse(ltloanTotalLoanPaidCtrl.text) ?? 0) +
                            (num.tryParse(stloanTotalLoanPaidCtrl.text) ?? 0),
                    'ltLoanReceived':
                        fbSocietyYearlyDataValue('ltLoanReceived') +
                            (num.tryParse(ltloanTotalLoanPaidCtrl.text) ?? 0),
                    'stLoanReceived':
                        fbSocietyYearlyDataValue('stLoanReceived') +
                            (num.tryParse(stloanTotalLoanPaidCtrl.text) ?? 0),
                    'ltLoanGiven': fbSocietyYearlyDataValue('ltLoanGiven') +
                        (num.tryParse(userPrevLtInstallmentCtrl.text) ?? 0),
                    'stLoanGiven': fbSocietyYearlyDataValue('stLoanGiven') +
                        (num.tryParse(userPrevStInstallmentCtrl.text) ?? 0),
                    'ltIntAmt': fbSocietyYearlyDataValue('ltIntAmt') +
                        (num.tryParse(userPrevLtInterestpaidCtrl.text) ?? 0),
                    'stIntAmt': fbSocietyYearlyDataValue('stIntAmt') +
                        (num.tryParse(userPrevStInterestpaidCtrl.text) ?? 0),
                    'totalIntAmt': fbSocietyYearlyDataValue('totalIntAmt') +
                        (num.tryParse(userPrevLtInterestpaidCtrl.text) ?? 0) +
                        (num.tryParse(userPrevStInterestpaidCtrl.text) ?? 0),
                    'loanIntRate':
                        num.tryParse(ctrl.settings!.ltloanInterest.toString()),
                    'totalPendingLoan':
                        fbSocietyYearlyDataValue('totalPendingLoan') +
                            ((num.tryParse(ltLoansDuectrl.text) ?? 0) +
                                (num.tryParse(stLoansDuectrl.text) ?? 0)) -
                            (num.tryParse(userPrevLtInstallmentpaidCtrl.text) ??
                                0) -
                            (num.tryParse(userPrevStInstallmentpaidCtrl.text) ??
                                0),
                    'ltPendingLoan': fbSocietyYearlyDataValue('ltPendingLoan') +
                        (num.tryParse(userPrevObltCtrl.text) ?? 0) -
                        (num.tryParse(userPrevLtLoanpaidCtrl.text) ?? 0),
                    'stPendingLoan': fbSocietyYearlyDataValue('stPendingLoan') +
                        (num.tryParse(userPrevObstCtrl.text) ?? 0) -
                        (num.tryParse(userPrevStLoanpaidCtrl.text) ?? 0),
                    'totalExpenses':
                        fbSocietyYearlyDataValue('totalExpenses') + 0,
                    'expensesIds': [],
                    'totalDividend': fbSocietyYearlyDataValue('totalDividend') +
                        (num.tryParse(totalsubsctrl.text) ?? 0) *
                            (num.tryParse(
                                    ctrl.settings?.dividentRate.toString() ??
                                        "0") ??
                                0),
                    'totalMonthlyShareGiven':
                        fbSocietyYearlyDataValue('totalMonthlyShareGiven') +
                            (num.tryParse(totalSharesctrl.text) ?? 0),
                    'totalShareGiven':
                        fbSocietyYearlyDataValue('totalShareGiven') +
                            (num.tryParse(totalSharesctrl.text) ?? 0),
                    'monthlyDividend': fbSocietyYearlyDataValue(
                            'monthlyDividend') +
                        (num.tryParse(totalsubsctrl.text) ?? 0) *
                            (num.tryParse(ctrl.settings?.dividentRate ?? "0") ??
                                0),
                    'dividendRate':
                        num.tryParse(ctrl.settings?.dividentRate ?? "0"),
                    'cashBalance': num.tryParse(0.toString()),
                  });
                }
              }
            } else {
              // Update existing user
              await FBFireStore.users.doc(usermodelw?.docId).update({
                'name': nctrl.text.isNotEmpty ? nctrl.text : 'Unknown',
                'cpfNo': cpfnoctrl.text.isNotEmpty
                    ? num.tryParse(cpfnoctrl.text)
                    : 'Unknown',
                'employeeNo': employeenoctrl.text.isNotEmpty
                    ? num.tryParse(employeenoctrl.text)
                    : '-',
                'currentAddress':
                    addressctrl.text.isNotEmpty ? addressctrl.text : '-',
                'permanentAddress':
                    paddressctrl.text.isNotEmpty ? paddressctrl.text : '-',
                'districtoffice': selectedofficeid ?? 'Unknown',
                'email': emailctrl.text.isNotEmpty
                    ? emailctrl.text.toLowerCase()
                    : '<EMAIL>',
                'phonenumber':
                    phonectrl.text.isNotEmpty ? phonectrl.text : '0000000000',
                // 'createdAt': DateTime.now(),
                'settlement': num.tryParse(settlementctrl.text) ?? 0,
                'totalSubs': num.tryParse(totalsubsctrl.text) ?? 0,
                'totalSubsInt': num.tryParse(totalSubsIntctrl.text) ?? 0,
                'ltLoansDue': num.tryParse(ltLoansDuectrl.text) ?? 0,
                'stLoansDue': num.tryParse(stLoansDuectrl.text) ?? 0,
                'totalLtLoans': num.tryParse(totalLtLoansctrl.text) ?? 0,
                'totalStLoans': num.tryParse(totalStLoansctrl.text) ?? 0,
                'totalLtIntPaid': num.tryParse(totalLtIntPaidctrl.text) ?? 0,
                'totalStIntPaid': num.tryParse(totalStIntPaidctrl.text) ?? 0,
                'totalDivident': num.tryParse(totalDividentctrl.text) ?? 0,
                'totalShares': num.tryParse(totalSharesctrl.text) ?? 0,
                'bankAcName': bankacnamectrl.text,
                'bankName': banknamectrl.text,
                'ifscCode': ifsccodectrl.text,
                'bankAcNo': num.tryParse(bankacnoctrl.text),
                'nomineeName': nomineeNamectrl.text,
                'nomineeRelation': nomineeRelationctrl.text,
                'userPrevoiusMonthlyRecord': {
                  'selectedyear': selectedYear,
                  'selectedmonth': selectedMonth,
                  'districtoffice': selectedofficeid ?? '',
                  'cpfNo': cpfnoctrl.text.isNotEmpty
                      ? num.tryParse(cpfnoctrl.text)
                      : '',
                  'name': nctrl.text.isNotEmpty ? nctrl.text.toUpperCase() : '',
                  'obLt': num.tryParse(userPrevObltCtrl.text) ?? 0,
                  'obSt': num.tryParse(userPrevObstCtrl.text) ?? 0,
                  'loanPaidLt': num.tryParse(userPrevLtLoanpaidCtrl.text) ?? 0,
                  'loanPaidst': num.tryParse(userPrevStLoanpaidCtrl.text) ?? 0,
                  'loanTotal': num.tryParse(userPrevTotalLoanCtrl.text) ?? 0,
                  'subs': num.tryParse(userPrevSubsCtrl.text) ?? 0,
                  'ltInstallment':
                      num.tryParse(userPrevLtInstallmentCtrl.text) ?? 0,
                  'stInstallment':
                      num.tryParse(userPrevStInstallmentCtrl.text) ?? 0,
                  'interest': num.tryParse(userPrevInterestCtrl.text) ?? 0,
                  'total': num.tryParse(userPrevLoanTotalSecondCtrl.text) ?? 0,
                  'installmentRec':
                      num.tryParse(userPrevInstallmentReceivedCtrl.text) ?? 0,
                  'installmentRecDate': installmentRecDate != null
                      ? Timestamp.fromDate(installmentRecDate!)
                      : null,
                  'ltCb': num.tryParse(userPrevCbltCtrl.text) ?? 0,
                  'stCb': num.tryParse(userPrevCbstCtrl.text) ?? 0,
                  'subscriptionPaid':
                      num.tryParse(userPrevSubsPaidCtrl.text) ?? 0,
                  'longTermInstalmentPaid':
                      num.tryParse(userPrevLtInstallmentpaidCtrl.text) ?? 0,
                  'shortTermInstalmentPaid':
                      num.tryParse(userPrevStInstallmentpaidCtrl.text) ?? 0,
                  'longTermInterestPaid':
                      num.tryParse(userPrevLtInterestpaidCtrl.text) ?? 0,
                  'shortTermInterestPaid':
                      num.tryParse(userPrevStInterestpaidCtrl.text) ?? 0,
                  'penaltyPaid':
                      num.tryParse(userPrevPenaltyPaidCtrl.text) ?? 0,
                  'totalReceived': 0,
                  'isPaid': (num.tryParse(userPrevLoanTotalSecondCtrl.text) ??
                          0) ==
                      (num.tryParse(userPrevInstallmentReceivedCtrl.text) ?? 0),
                  'status': (num.tryParse(userPrevLoanTotalSecondCtrl.text) ??
                              0) ==
                          ((num.tryParse(
                                  userPrevInstallmentReceivedCtrl.text) ??
                              0))
                      ? 'Paid'
                      : ((((num.tryParse(
                                      userPrevInstallmentReceivedCtrl.text) ??
                                  0))) >
                              0
                          ? 'Partially Paid'
                          : 'Unpaid'),
                },
              });
              // print("User Updated");
              showCtcAppSnackBar(context, "User Successfully Updated.");
            }

            if (context.mounted) {
              context.go(Routes.users);
            }
          } catch (e) {
            // print("Error: $e");
            debugPrint(e.toString());
            showCtcAppSnackBar(context, "An error occurred. Please try again.");
          } finally {
            setState(() {
              isLoading = false;
            });
          }
        }
      },
    );
  }

  Future<dynamic> iconsdialog(BuildContext context, Function() cameraonPressed,
      Function() fileonPressed) {
    return showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text("Upload File From"),
        content: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            IconButton(
                onPressed: cameraonPressed,
                icon: const Icon(Icons.camera_alt_outlined)),
            IconButton(
                onPressed: fileonPressed, icon: const Icon(Icons.attach_file))
          ],
        ),
      ),
    );
  }
}

class PersonalDetailsSection extends StatelessWidget {
  final TextEditingController nctrl,
      cpfnoctrl,
      employeenoctrl,
      addressctrl,
      paddressctrl,
      phonectrl,
      emailctrl;
  final String? selectedofficeid;
  final Function(String?) onOfficeChanged;
  final List<dynamic> districtOffices;
  final bool isAddMode;

  const PersonalDetailsSection({
    super.key,
    required this.nctrl,
    required this.cpfnoctrl,
    required this.employeenoctrl,
    required this.addressctrl,
    required this.paddressctrl,
    required this.selectedofficeid,
    required this.onOfficeChanged,
    required this.districtOffices,
    required this.phonectrl,
    required this.emailctrl,
    required this.isAddMode,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text("PERSONAL DETAILS : ",
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
        const SizedBox(height: 20),
        StaggeredGrid.count(
          crossAxisSpacing: 50,
          crossAxisCount: 4,
          children: [
            CustomTextfield(text: 'Name *', controller: nctrl, enabled: true),
            CustomTextfield(
                text: 'CPF No. *', controller: cpfnoctrl, enabled: true),
            CustomTextfield(
                text: 'Employee No. *',
                controller: employeenoctrl,
                enabled: true),
            CustomTextfield(
                text: 'Current Address *',
                controller: addressctrl,
                enabled: true),
            CustomTextfield(
                text: 'Permanent Address *',
                controller: paddressctrl,
                enabled: true),
            IntrinsicHeight(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text("District Office *"),
                  DropdownButtonHideUnderline(
                    child: DropdownButtonFormField(
                      decoration: InputDecoration(
                          border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(10))),
                      value: selectedofficeid,
                      items: [
                        ...List.generate(
                          districtOffices.length,
                          (index) => DropdownMenuItem<String>(
                            value: districtOffices[index].docId,
                            child: Text(districtOffices[index].name),
                          ),
                        ),
                      ],
                      onChanged: onOfficeChanged,
                    ),
                  ),
                  const SizedBox(height: 20),
                ],
              ),
            ),
            CustomTextfield(
                enabled: true, text: 'Phone No. *', controller: phonectrl),
            CustomTextfield(
              enabled: isAddMode,
              text: 'Email *',
              controller: emailctrl,
              validatorrr: (value) {
                if (value == null || value.isEmpty) {
                  return showCtcAppSnackBar(
                      context, "Valid email is required!");
                }
                return null;
              },
            ),
          ],
        ),
      ],
    );
  }
}

class BankDetailsSection extends StatelessWidget {
  final TextEditingController bankacnamectrl,
      banknamectrl,
      bankacnoctrl,
      ifsccodectrl;
  const BankDetailsSection({
    super.key,
    required this.bankacnamectrl,
    required this.banknamectrl,
    required this.bankacnoctrl,
    required this.ifsccodectrl,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text("BANK DETAILS : ",
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
        const SizedBox(height: 20),
        StaggeredGrid.count(
          crossAxisSpacing: 50,
          crossAxisCount: 4,
          children: [
            CustomTextfield(
                enabled: true,
                text: 'Bank Account Name *',
                controller: bankacnamectrl),
            CustomTextfield(
                enabled: true,
                text: 'Bank Name *. (HDFC, SBI, etc)',
                controller: banknamectrl),
            CustomTextfield(
                enabled: true,
                text: 'Bank Account Number *',
                controller: bankacnoctrl),
            CustomTextfield(
                enabled: true, text: 'IFSC *', controller: ifsccodectrl),
          ],
        ),
      ],
    );
  }
}

class OthersSection extends StatelessWidget {
  final TextEditingController totalSharesctrl;
  // final TextEditingController totalDividentctrl;
  const OthersSection({
    super.key,
    required this.totalSharesctrl,
    // required this.totalDividentctrl,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text("OTHERS : ",
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
        const SizedBox(height: 20),
        StaggeredGrid.count(
          crossAxisCount: 2,
          crossAxisSpacing: 50,
          children: [
            CustomTextfield(
                enabled: true,
                text: 'Total Shares Value *',
                controller: totalSharesctrl),
            // CustomTextfield(enabled: true, text: 'Total Divident *', controller: totalDividentctrl),
          ],
        ),
      ],
    );
  }
}

class DocumentSection extends StatelessWidget {
  final String? userdocId;
  final UserModel? usermodelw;
  final String? documentUrl;
  final bool isUploadingDoc;
  final VoidCallback onUpload;
  final VoidCallback onView;

  const DocumentSection({
    super.key,
    required this.userdocId,
    required this.usermodelw,
    required this.documentUrl,
    required this.isUploadingDoc,
    required this.onUpload,
    required this.onView,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text.rich(
          TextSpan(
            children: [
              const TextSpan(
                text: 'DOCUMENT * ',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const TextSpan(
                text:
                    ' (common pdf: Aadhar Card, Office Identity Card, Bank Account Details with IFSC, Latest Salary Slip)',
                style: TextStyle(fontSize: 14, fontWeight: FontWeight.normal),
              ),
              const TextSpan(
                text: ' :',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
            ],
          ),
        ),
        const SizedBox(height: 20),
        SizedBox(
          width: MediaQuery.sizeOf(context).width,
          height: 50,
          child: ListTile(
            title: Text(
              userdocId != null
                  ? (usermodelw?.documents ?? "No document")
                  : (documentUrl ?? "No document selected"),
              style: const TextStyle(color: Colors.black),
              overflow: TextOverflow.ellipsis,
            ),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                IconButton(
                    icon: const Icon(Icons.visibility), onPressed: onView),
                IconButton(
                  icon: isUploadingDoc
                      ? const SizedBox(
                          width: 24,
                          height: 24,
                          child: CircularProgressIndicator(strokeWidth: 2))
                      : const Icon(Icons.upload_file),
                  onPressed: isUploadingDoc ? null : onUpload,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}

class LongTermLoanSection extends StatelessWidget {
  final bool ltLoanCheck;
  final ValueChanged<bool?> onCheckChanged;
  final DateTime? ltAppliedOnDate;
  final DateTime? ltApprovedOnDate;
  final VoidCallback onAppliedOnTap;
  final VoidCallback onApprovedOnTap;
  // Add all controllers needed for the fields
  final TextEditingController ltloanTypeCtrl;
  final TextEditingController ltloanAppNoCtrl;
  final TextEditingController ltloanDesigationCtrl;
  final TextEditingController ltloanReasonCtrl;
  final TextEditingController lloanAppliedLoanAmtCtrl;
  final TextEditingController ltloanTotalAmtCtrl;
  final TextEditingController ltloanAppliedLoanAmtInWordsCtrl;
  final TextEditingController ltloanMonthlyInstallmentAmtCtrl;
  final TextEditingController ltloanTotalLoanPaidCtrl;
  final TextEditingController ltloanTotalInterestPaidCtrl;
  final TextEditingController ltloanTotalLoanDueCtrl;
  final TextEditingController ltloanShareCtrl;
  final TextEditingController ltloanBankAccNumCtrl;
  final TextEditingController ltloanLtSurityName1Ctrl;
  final TextEditingController ltloanLtSurityName2Ctrl;
  final TextEditingController ltloanLtSurityAcc1Ctrl;
  final TextEditingController ltloanLtSurityAcc2Ctrl;
  final String? ltloanUserSignuploadedUrl;
  final String? ltloanUserDocUploadedUrl;
  final bool isUploadingltBSign;
  final bool isUploadingltDoc;
  final Future<void> Function() onSignUpload;
  final Future<void> Function() onDocUpload;
  final VoidCallback onRemoveSign;
  final VoidCallback onRemoveDoc;
  final VoidCallback onViewSign;
  final VoidCallback onViewDoc;

  const LongTermLoanSection({
    super.key,
    required this.ltLoanCheck,
    required this.onCheckChanged,
    required this.ltAppliedOnDate,
    required this.ltApprovedOnDate,
    required this.onAppliedOnTap,
    required this.onApprovedOnTap,
    required this.ltloanTypeCtrl,
    required this.ltloanAppNoCtrl,
    required this.ltloanDesigationCtrl,
    required this.ltloanReasonCtrl,
    required this.lloanAppliedLoanAmtCtrl,
    required this.ltloanTotalAmtCtrl,
    required this.ltloanAppliedLoanAmtInWordsCtrl,
    required this.ltloanMonthlyInstallmentAmtCtrl,
    required this.ltloanTotalLoanPaidCtrl,
    required this.ltloanTotalInterestPaidCtrl,
    required this.ltloanTotalLoanDueCtrl,
    required this.ltloanShareCtrl,
    required this.ltloanBankAccNumCtrl,
    required this.ltloanLtSurityName1Ctrl,
    required this.ltloanLtSurityName2Ctrl,
    required this.ltloanLtSurityAcc1Ctrl,
    required this.ltloanLtSurityAcc2Ctrl,
    required this.ltloanUserSignuploadedUrl,
    required this.ltloanUserDocUploadedUrl,
    required this.isUploadingltBSign,
    required this.isUploadingltDoc,
    required this.onSignUpload,
    required this.onDocUpload,
    required this.onRemoveSign,
    required this.onRemoveDoc,
    required this.onViewSign,
    required this.onViewDoc,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(children: [
          const Text(
            "LONG TERM LOAN DETAILS : ",
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          Checkbox(
            value: ltLoanCheck,
            onChanged: onCheckChanged,
          ),
        ]),
        const SizedBox(height: 20),
        if (ltLoanCheck) ...[
          // Dates
          StaggeredGrid.count(
            crossAxisCount: 3,
            crossAxisSpacing: 50,
            children: [
              // Applied On
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text("Applied On * : ", style: TextStyle(fontSize: 15)),
                  InkWell(
                    onTap: onAppliedOnTap,
                    child: TextFormField(
                      style: const TextStyle(color: Colors.black),
                      enabled: false,
                      controller: TextEditingController(
                        text: ltAppliedOnDate
                                ?.toLocal()
                                .toString()
                                .split(' ')[0] ??
                            'Select Date',
                      ),
                      decoration: InputDecoration(
                        filled: true,
                        prefixIcon: const Icon(Icons.date_range,
                            size: 20, color: Colors.black),
                        border: OutlineInputBorder(
                          borderSide: BorderSide.none,
                          borderRadius: BorderRadius.circular(6),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              // Approved On
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text("Approved On * : ",
                      style: TextStyle(fontSize: 15)),
                  InkWell(
                    onTap: onApprovedOnTap,
                    child: TextFormField(
                      style: const TextStyle(color: Colors.black),
                      enabled: false,
                      controller: TextEditingController(
                        text: ltApprovedOnDate
                                ?.toLocal()
                                .toString()
                                .split(' ')[0] ??
                            'Select Date',
                      ),
                      decoration: InputDecoration(
                        filled: true,
                        prefixIcon: const Icon(Icons.date_range,
                            size: 20, color: Colors.black),
                        border: OutlineInputBorder(
                          borderSide: BorderSide.none,
                          borderRadius: BorderRadius.circular(6),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 20),
          // Fields
          StaggeredGrid.count(
            crossAxisCount: 3,
            crossAxisSpacing: 50,
            children: [
              CustomTextfield(
                  enabled: false,
                  text: 'Loan Type *',
                  controller: ltloanTypeCtrl),
              CustomTextfield(
                  enabled: true,
                  text: 'Application No *',
                  controller: ltloanAppNoCtrl),
              CustomTextfield(
                  enabled: true,
                  text: 'Designation *',
                  controller: ltloanDesigationCtrl),
              CustomTextfield(
                  enabled: true,
                  text: 'Loan Reason *',
                  controller: ltloanReasonCtrl),
              CustomTextfield(
                  enabled: true,
                  text: 'Total Loan Amount *',
                  controller: lloanAppliedLoanAmtCtrl),
              CustomTextfield(
                  enabled: true,
                  text: 'Applied Loan Amount ( in ₹ ) *',
                  controller: ltloanTotalAmtCtrl),
              CustomTextfield(
                  enabled: true,
                  text: 'Applied Loan Amount in Words *',
                  controller: ltloanAppliedLoanAmtInWordsCtrl),
              CustomTextfield(
                  enabled: true,
                  text: 'Monthly Installment Amount *',
                  controller: ltloanMonthlyInstallmentAmtCtrl),
              CustomTextfield(
                  enabled: true,
                  text: 'Total Loan Paid *',
                  controller: ltloanTotalLoanPaidCtrl),
              CustomTextfield(
                  enabled: true,
                  text: 'Total Interest Paid *',
                  controller: ltloanTotalInterestPaidCtrl),
              CustomTextfield(
                  enabled: true,
                  text: 'Total Loan Due *',
                  controller: ltloanTotalLoanDueCtrl),
              CustomTextfield(
                  enabled: true, text: 'Share *', controller: ltloanShareCtrl),
              CustomTextfield(
                  enabled: true,
                  text: 'Bank Account Number *',
                  controller: ltloanBankAccNumCtrl),
              CustomTextfield(
                  enabled: true,
                  text: 'Surity Name 1 *',
                  controller: ltloanLtSurityName1Ctrl),
              CustomTextfield(
                  enabled: true,
                  text: 'Surity Name 2 *',
                  controller: ltloanLtSurityName2Ctrl),
              CustomTextfield(
                  enabled: true,
                  text: 'Account Number of Surity Name 1',
                  controller: ltloanLtSurityAcc1Ctrl),
              CustomTextfield(
                  enabled: true,
                  text: 'Account Number of Surity Name 2',
                  controller: ltloanLtSurityAcc2Ctrl),
            ],
          ),

          // Signature Upload
          Row(
            children: [
              Expanded(
                child: ltloanUserSignuploadedUrl == null
                    ? isUploadingltBSign
                        ? const Padding(
                            padding: EdgeInsets.only(top: 12),
                            child: Center(child: CircularProgressIndicator()),
                          )
                        : Padding(
                            padding: const EdgeInsets.only(top: 12),
                            child: ElevatedButton(
                              style: ElevatedButton.styleFrom(
                                padding: const EdgeInsets.all(10),
                                fixedSize:
                                    Size(MediaQuery.sizeOf(context).width, 40),
                                backgroundColor: Colors.green.shade300,
                                elevation: 0,
                                foregroundColor: Colors.white,
                                shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(4)),
                              ),
                              onPressed: onSignUpload,
                              child: const Text(
                                textAlign: TextAlign.center,
                                "UPLOAD BORROWER'S SIGNATURE PDF",
                                style: TextStyle(
                                    color: Colors.white, fontSize: 15),
                              ),
                            ),
                          )
                    : Row(
                        children: [
                          Expanded(
                            child: Text(
                              maxLines: 3,
                              'Sign Uploaded: $ltloanUserSignuploadedUrl',
                              overflow: TextOverflow.ellipsis,
                              style: const TextStyle(
                                  color: Colors.green, fontSize: 15),
                            ),
                          ),
                          IconButton(
                            icon:
                                const Icon(Icons.download, color: Colors.blue),
                            tooltip: "Open PDF in browser",
                            onPressed: onViewSign,
                          ),
                          IconButton(
                            onPressed: onRemoveSign,
                            icon: const Icon(Icons.close, color: Colors.red),
                            tooltip: 'Remove document',
                          ),
                        ],
                      ),
              ),
              const SizedBox(width: 20),
              // Document Upload
              Expanded(
                child: ltloanUserDocUploadedUrl == null
                    ? isUploadingltDoc
                        ? const Padding(
                            padding: EdgeInsets.only(top: 12),
                            child: Center(child: CircularProgressIndicator()),
                          )
                        : Padding(
                            padding: const EdgeInsets.only(top: 12),
                            child: ElevatedButton(
                              style: ElevatedButton.styleFrom(
                                padding: const EdgeInsets.all(10),
                                fixedSize:
                                    Size(MediaQuery.sizeOf(context).width, 40),
                                backgroundColor: Colors.green.shade300,
                                elevation: 0,
                                foregroundColor: Colors.white,
                                shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(4)),
                              ),
                              onPressed: onDocUpload,
                              child: const Text(
                                textAlign: TextAlign.center,
                                "UPLOAD DOCUMENTS PDF",
                                style: TextStyle(
                                    color: Colors.white, fontSize: 15),
                              ),
                            ),
                          )
                    : Row(
                        children: [
                          Expanded(
                            child: Text(
                              maxLines: 3,
                              'PDF Uploaded: $ltloanUserDocUploadedUrl',
                              overflow: TextOverflow.ellipsis,
                              style: const TextStyle(
                                  color: Colors.green, fontSize: 15),
                            ),
                          ),
                          IconButton(
                            icon:
                                const Icon(Icons.download, color: Colors.blue),
                            tooltip: "Open PDF in browser",
                            onPressed: onViewDoc,
                          ),
                          IconButton(
                            onPressed: onRemoveDoc,
                            icon: const Icon(Icons.close, color: Colors.red),
                            tooltip: 'Remove document',
                          ),
                        ],
                      ),
              ),
            ],
          ),
        ],
      ],
    );
  }
}

class ShortTermLoanSection extends StatelessWidget {
  final bool stLoanCheck;
  final ValueChanged<bool?> onCheckChanged;
  final DateTime? stAppliedOnDate;
  final DateTime? stApprovedOnDate;
  final VoidCallback onAppliedOnTap;
  final VoidCallback onApprovedOnTap;
  final TextEditingController stloanTypeCtrl;
  final TextEditingController stloanAppNoCtrl;
  final TextEditingController stloanReasonCtrl;
  final TextEditingController stloanTotalLoanAmtCtrl;
  final TextEditingController stloanAppliedLoanAmtCtrl;
  final TextEditingController stloanAppliedLoanAmtInWordsCtrl;
  final TextEditingController stloanMonthlyInstallmentAmtCtrl;
  final TextEditingController stloanTotalLoanPaidCtrl;
  final TextEditingController stloanTotalInterestPaidCtrl;
  final TextEditingController stloanTotalLoanDueCtrl;
  final TextEditingController stloanShareCtrl;
  final TextEditingController stloanBankAccNumCtrl;
  final TextEditingController stloanLtSurityName1Ctrl;
  final TextEditingController stloanLtSurityName2Ctrl;
  final String? stloanUserSignuploadedUrl;
  final String? stloanUserDocUploadedUrl;
  final bool isUploadingStBSign;
  final bool isUploadingStDoc;
  final Future<void> Function() onSignUpload;
  final Future<void> Function() onDocUpload;
  final VoidCallback onRemoveSign;
  final VoidCallback onRemoveDoc;
  final VoidCallback onViewSign;
  final VoidCallback onViewDoc;

  const ShortTermLoanSection({
    super.key,
    required this.stLoanCheck,
    required this.onCheckChanged,
    required this.stAppliedOnDate,
    required this.stApprovedOnDate,
    required this.onAppliedOnTap,
    required this.onApprovedOnTap,
    required this.stloanTypeCtrl,
    required this.stloanAppNoCtrl,
    required this.stloanReasonCtrl,
    required this.stloanTotalLoanAmtCtrl,
    required this.stloanAppliedLoanAmtCtrl,
    required this.stloanAppliedLoanAmtInWordsCtrl,
    required this.stloanMonthlyInstallmentAmtCtrl,
    required this.stloanTotalLoanPaidCtrl,
    required this.stloanTotalInterestPaidCtrl,
    required this.stloanTotalLoanDueCtrl,
    required this.stloanShareCtrl,
    required this.stloanBankAccNumCtrl,
    required this.stloanLtSurityName1Ctrl,
    required this.stloanLtSurityName2Ctrl,
    required this.stloanUserSignuploadedUrl,
    required this.stloanUserDocUploadedUrl,
    required this.isUploadingStBSign,
    required this.isUploadingStDoc,
    required this.onSignUpload,
    required this.onDocUpload,
    required this.onRemoveSign,
    required this.onRemoveDoc,
    required this.onViewSign,
    required this.onViewDoc,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(children: [
          const Text(
            "SHORT TERM LOAN DETAILS : ",
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          Checkbox(
            value: stLoanCheck,
            onChanged: onCheckChanged,
          ),
        ]),
        const SizedBox(height: 20),
        if (stLoanCheck) ...[
          // Dates
          StaggeredGrid.count(
            crossAxisCount: 3,
            crossAxisSpacing: 50,
            children: [
              // Applied On
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text("Applied On * : ", style: TextStyle(fontSize: 15)),
                  InkWell(
                    onTap: onAppliedOnTap,
                    child: TextFormField(
                      style: const TextStyle(color: Colors.black),
                      enabled: false,
                      controller: TextEditingController(
                        text: stAppliedOnDate
                                ?.toLocal()
                                .toString()
                                .split(' ')[0] ??
                            'Select Date',
                      ),
                      decoration: InputDecoration(
                        filled: true,
                        prefixIcon: const Icon(Icons.date_range,
                            size: 20, color: Colors.black),
                        border: OutlineInputBorder(
                          borderSide: BorderSide.none,
                          borderRadius: BorderRadius.circular(6),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              // Approved On
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text("Approved On * : ",
                      style: TextStyle(fontSize: 15)),
                  InkWell(
                    onTap: onApprovedOnTap,
                    child: TextFormField(
                      style: const TextStyle(color: Colors.black),
                      enabled: false,
                      controller: TextEditingController(
                        text: stApprovedOnDate
                                ?.toLocal()
                                .toString()
                                .split(' ')[0] ??
                            'Select Date',
                      ),
                      decoration: InputDecoration(
                        filled: true,
                        prefixIcon: const Icon(Icons.date_range,
                            size: 20, color: Colors.black),
                        border: OutlineInputBorder(
                          borderSide: BorderSide.none,
                          borderRadius: BorderRadius.circular(6),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 20),
          // Fields
          StaggeredGrid.count(
            crossAxisCount: 3,
            crossAxisSpacing: 50,
            children: [
              CustomTextfield(
                  enabled: false,
                  text: 'Loan Type *',
                  controller: stloanTypeCtrl),
              CustomTextfield(
                  enabled: true,
                  text: 'Application No *',
                  controller: stloanAppNoCtrl),
              CustomTextfield(
                  enabled: true,
                  text: 'Loan Reason *',
                  controller: stloanReasonCtrl),
              CustomTextfield(
                  enabled: true,
                  text: 'Total Loan Amount *',
                  controller: stloanTotalLoanAmtCtrl),
              CustomTextfield(
                  enabled: true,
                  text: 'Applied Loan Amount ( in ₹ ) *',
                  controller: stloanAppliedLoanAmtCtrl),
              CustomTextfield(
                  enabled: true,
                  text: 'Applied Loan Amount in Words *',
                  controller: stloanAppliedLoanAmtInWordsCtrl),
              CustomTextfield(
                  enabled: true,
                  text: 'Monthly Installment Amount *',
                  controller: stloanMonthlyInstallmentAmtCtrl),
              CustomTextfield(
                  enabled: true,
                  text: 'Total Loan Paid *',
                  controller: stloanTotalLoanPaidCtrl),
              CustomTextfield(
                  enabled: true,
                  text: 'Total Interest Paid *',
                  controller: stloanTotalInterestPaidCtrl),
              CustomTextfield(
                  enabled: true,
                  text: 'Total Loan Due *',
                  controller: stloanTotalLoanDueCtrl),
              CustomTextfield(
                  enabled: true, text: 'Share *', controller: stloanShareCtrl),
              CustomTextfield(
                  enabled: true,
                  text: 'Bank Account Number *',
                  controller: stloanBankAccNumCtrl),
              CustomTextfield(
                  enabled: true,
                  text: 'Surity Name 1 *',
                  controller: stloanLtSurityName1Ctrl),
              CustomTextfield(
                  enabled: true,
                  text: 'Surity Name 2 *',
                  controller: stloanLtSurityName2Ctrl),
            ],
          ),
          // Signature & Document Upload in one line
          Row(
            children: [
              Expanded(
                child: stloanUserSignuploadedUrl == null
                    ? isUploadingStBSign
                        ? const Padding(
                            padding: EdgeInsets.only(top: 12),
                            child: Center(child: CircularProgressIndicator()),
                          )
                        : Padding(
                            padding: const EdgeInsets.only(top: 12),
                            child: ElevatedButton(
                              style: ElevatedButton.styleFrom(
                                padding: const EdgeInsets.all(10),
                                fixedSize:
                                    Size(MediaQuery.sizeOf(context).width, 40),
                                backgroundColor: Colors.green.shade300,
                                elevation: 0,
                                foregroundColor: Colors.white,
                                shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(4)),
                              ),
                              onPressed: onSignUpload,
                              child: const Text(
                                textAlign: TextAlign.center,
                                "UPLOAD BORROWER'S SIGNATURE PDF",
                                style: TextStyle(
                                    color: Colors.white, fontSize: 15),
                              ),
                            ),
                          )
                    : Row(
                        children: [
                          Expanded(
                            child: Text(
                              maxLines: 3,
                              'Sign Uploaded: $stloanUserSignuploadedUrl',
                              overflow: TextOverflow.ellipsis,
                              style: const TextStyle(
                                  color: Colors.green, fontSize: 15),
                            ),
                          ),
                          IconButton(
                            icon:
                                const Icon(Icons.download, color: Colors.blue),
                            tooltip: "Open PDF in browser",
                            onPressed: onViewSign,
                          ),
                          IconButton(
                            onPressed: onRemoveSign,
                            icon: const Icon(Icons.close, color: Colors.red),
                            tooltip: 'Remove document',
                          ),
                        ],
                      ),
              ),
              const SizedBox(width: 20),
              Expanded(
                child: stloanUserDocUploadedUrl == null
                    ? isUploadingStDoc
                        ? const Padding(
                            padding: EdgeInsets.only(top: 12),
                            child: Center(child: CircularProgressIndicator()),
                          )
                        : Padding(
                            padding: const EdgeInsets.only(top: 12),
                            child: ElevatedButton(
                              style: ElevatedButton.styleFrom(
                                padding: const EdgeInsets.all(10),
                                fixedSize:
                                    Size(MediaQuery.sizeOf(context).width, 40),
                                backgroundColor: Colors.green.shade300,
                                elevation: 0,
                                foregroundColor: Colors.white,
                                shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(4)),
                              ),
                              onPressed: onDocUpload,
                              child: const Text(
                                textAlign: TextAlign.center,
                                "UPLOAD DOCUMENTS PDF",
                                style: TextStyle(
                                    color: Colors.white, fontSize: 15),
                              ),
                            ),
                          )
                    : Row(
                        children: [
                          Expanded(
                            child: Text(
                              maxLines: 3,
                              'PDF Uploaded: $stloanUserDocUploadedUrl',
                              overflow: TextOverflow.ellipsis,
                              style: const TextStyle(
                                  color: Colors.green, fontSize: 15),
                            ),
                          ),
                          IconButton(
                            icon:
                                const Icon(Icons.download, color: Colors.blue),
                            tooltip: "Open PDF in browser",
                            onPressed: onViewDoc,
                          ),
                          IconButton(
                            onPressed: onRemoveDoc,
                            icon: const Icon(Icons.close, color: Colors.red),
                            tooltip: 'Remove document',
                          ),
                        ],
                      ),
              ),
            ],
          ),
        ],
      ],
    );
  }
}

class SubsDetailsSection extends StatelessWidget {
  final TextEditingController totalsubsctrl;
  // final TextEditingController totalSubsIntctrl;

  const SubsDetailsSection({
    super.key,
    required this.totalsubsctrl,
    // required this.totalSubsIntctrl,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text.rich(
          TextSpan(
            children: [
              const TextSpan(
                text: 'SUBS DETAILS ',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const TextSpan(
                text:
                    ' (Current Value till date - regardless of recovery sheet sent) : ',
                style: TextStyle(fontSize: 14, fontWeight: FontWeight.normal),
              ),
            ],
          ),
        ),
        const SizedBox(height: 20),
        StaggeredGrid.count(
          crossAxisCount: 3,
          crossAxisSpacing: 50,
          children: [
            CustomTextfield(
              enabled: true,
              text: 'Total Subscription *',
              controller: totalsubsctrl,
            ),
            // CustomTextfield(
            //   enabled: true,
            //   text: 'Total Subscription Interest *',
            //   controller: totalSubsIntctrl,
            // ),
          ],
        ),
      ],
    );
  }
}

class StLoanDetailsSection extends StatelessWidget {
  final TextEditingController totalStLoansctrl;
  final TextEditingController stLoansDuectrl;
  final TextEditingController totalStIntPaidctrl;

  const StLoanDetailsSection({
    super.key,
    required this.totalStLoansctrl,
    required this.stLoansDuectrl,
    required this.totalStIntPaidctrl,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text.rich(
          TextSpan(
            children: [
              const TextSpan(
                text: 'ST LOAN DETAILS ',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const TextSpan(
                text:
                    ' (Current Value till date - regardless of recovery sheet sent) : ',
                style: TextStyle(fontSize: 14, fontWeight: FontWeight.normal),
              ),
            ],
          ),
        ),
        const SizedBox(height: 20),
        StaggeredGrid.count(
          crossAxisCount: 3,
          crossAxisSpacing: 50,
          children: [
            CustomTextfield(
              enabled: true,
              text: 'Total St Loans (₹) *',
              controller: totalStLoansctrl,
            ),
            CustomTextfield(
              enabled: true,
              text: 'St Loans Due (₹) *',
              controller: stLoansDuectrl,
            ),
            CustomTextfield(
              enabled: true,
              text: 'Total St Interest Paid (₹) *',
              controller: totalStIntPaidctrl,
            ),
          ],
        ),
      ],
    );
  }
}

class LtLoanDetailsSection extends StatelessWidget {
  final TextEditingController totalLtLoansctrl;
  final TextEditingController ltLoansDuectrl;
  final TextEditingController totalLtIntPaidctrl;
  // final TextEditingController ltAmtPaidTillNowCtrl;
  // final TextEditingController ltEmiPaidTillNowMonthsCtrl;

  const LtLoanDetailsSection({
    super.key,
    required this.totalLtLoansctrl,
    required this.ltLoansDuectrl,
    required this.totalLtIntPaidctrl,
    // this.ltAmtPaidTillNowCtrl,
    // this.ltEmiPaidTillNowMonthsCtrl,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text.rich(
          TextSpan(
            children: [
              const TextSpan(
                text: 'LT LOAN DETAILS ',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const TextSpan(
                text:
                    ' (Current Value till date - regardless of recovery sheet sent) : ',
                style: TextStyle(fontSize: 14, fontWeight: FontWeight.normal),
              ),
            ],
          ),
        ),
        const SizedBox(height: 20),
        StaggeredGrid.count(
          crossAxisCount: 3,
          crossAxisSpacing: 50,
          children: [
            CustomTextfield(
              enabled: true,
              text: 'Total Lt Loans (₹) *',
              controller: totalLtLoansctrl,
            ),
            CustomTextfield(
              enabled: true,
              text: 'Lt Loans Due (₹) *',
              controller: ltLoansDuectrl,
            ),
            CustomTextfield(
              enabled: true,
              text: 'Total Lt Interest Paid (₹) *',
              controller: totalLtIntPaidctrl,
            ),
          ],
        ),
      ],
    );
  }
}

class LastMonthRecoveryDetailsSection extends StatelessWidget {
  final int? selectedMonth;
  final int? selectedYear;
  final List<int> yearList;
  final List<String> month;
  final ValueChanged<int?> onMonthChanged;
  final ValueChanged<int?> onYearChanged;
  final TextEditingController userPrevObltCtrl;
  final TextEditingController userPrevObstCtrl;
  final TextEditingController userPrevLtLoanpaidCtrl;
  final TextEditingController userPrevStLoanpaidCtrl;
  final TextEditingController userPrevTotalLoanCtrl;
  final TextEditingController userPrevSubsCtrl;
  final TextEditingController userPrevLtInstallmentCtrl;
  final TextEditingController userPrevStInstallmentCtrl;
  final TextEditingController userPrevInterestCtrl;
  final TextEditingController duesCtrl;
  final TextEditingController penaltyCtrl;
  final TextEditingController userPrevLoanTotalSecondCtrl;
  final TextEditingController userPrevCbltCtrl;
  final TextEditingController userPrevCbstCtrl;
  final TextEditingController userPrevInstallmentReceivedCtrl;
  final DateTime? installmentRecDate;
  final VoidCallback onInstallmentRecDateTap;
  final TextEditingController userPrevSubsPaidCtrl;
  final TextEditingController userPrevLtInterestpaidCtrl;
  final TextEditingController userPrevStInterestpaidCtrl;
  final TextEditingController userPrevLtInstallmentpaidCtrl;
  final TextEditingController userPrevStInstallmentpaidCtrl;
  final TextEditingController userPrevPenaltyPaidCtrl;

  const LastMonthRecoveryDetailsSection({
    super.key,
    required this.selectedMonth,
    required this.selectedYear,
    required this.yearList,
    required this.month,
    required this.onMonthChanged,
    required this.onYearChanged,
    required this.userPrevObltCtrl,
    required this.userPrevObstCtrl,
    required this.userPrevLtLoanpaidCtrl,
    required this.userPrevStLoanpaidCtrl,
    required this.userPrevTotalLoanCtrl,
    required this.userPrevSubsCtrl,
    required this.userPrevLtInstallmentCtrl,
    required this.userPrevStInstallmentCtrl,
    required this.userPrevInterestCtrl,
    required this.duesCtrl,
    required this.penaltyCtrl,
    required this.userPrevLoanTotalSecondCtrl,
    required this.userPrevCbltCtrl,
    required this.userPrevCbstCtrl,
    required this.userPrevInstallmentReceivedCtrl,
    required this.installmentRecDate,
    required this.onInstallmentRecDateTap,
    required this.userPrevSubsPaidCtrl,
    required this.userPrevLtInterestpaidCtrl,
    required this.userPrevStInterestpaidCtrl,
    required this.userPrevLtInstallmentpaidCtrl,
    required this.userPrevStInstallmentpaidCtrl,
    required this.userPrevPenaltyPaidCtrl,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          "LAST MONTH RECOVERY DETAILS : ",
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 20),
        // Month & Year Dropdowns
        StaggeredGrid.count(
          crossAxisCount: 3,
          crossAxisSpacing: 50,
          children: [
            DropdownButtonHideUnderline(
              child: DropdownButtonFormField<int>(
                focusColor: Colors.transparent,
                dropdownColor: Colors.white,
                value: selectedMonth,
                decoration: InputDecoration(
                  hintText: "Select Month",
                  constraints:
                      const BoxConstraints(maxWidth: 150, maxHeight: 45),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(5),
                  ),
                ),
                items: List.generate(
                  selectedYear == DateTime.now().year
                      ? DateTime.now().month + 1
                      : 12,
                  (index) => DropdownMenuItem(
                    value: index + 1,
                    child: Text(month[index]),
                  ),
                ),
                onChanged: onMonthChanged,
              ),
            ),
            DropdownButtonHideUnderline(
              child: DropdownButtonFormField<int>(
                focusColor: Colors.transparent,
                dropdownColor: Colors.white,
                value: selectedYear,
                decoration: InputDecoration(
                  hintText: "Select Year",
                  constraints:
                      const BoxConstraints(maxWidth: 150, maxHeight: 45),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(5),
                  ),
                ),
                items: List.generate(
                  yearList.length,
                  (index) => DropdownMenuItem(
                    value: yearList[index],
                    child: Text(yearList[index].toString()),
                  ),
                ),
                onChanged: onYearChanged,
              ),
            ),
          ],
        ),
        const SizedBox(height: 20),
        // Oblt, Obst, LT Loan Paid, ST Loan Paid
        StaggeredGrid.count(
          crossAxisCount: 3,
          crossAxisSpacing: 50,
          children: [
            CustomTextfield(
              enabled: true,
              text: 'Oblt *',
              controller: userPrevObltCtrl,
            ),
            CustomTextfield(
              enabled: true,
              text: 'Obst *',
              controller: userPrevObstCtrl,
            ),
            CustomTextfield(
              enabled: true,
              text: 'LT Loan Paid * (new loans approved this month)',
              controller: userPrevLtLoanpaidCtrl,
            ),
            CustomTextfield(
              enabled: true,
              text: 'ST Loan Paid * (new loans approved this month)',
              controller: userPrevStLoanpaidCtrl,
            ),
          ],
        ),
        StaggeredGrid.count(
          crossAxisCount: 3,
          crossAxisSpacing: 50,
          children: [
            CustomTextfield(
              enabled: true,
              text: 'Total Loan * (oblt + obst + Lt Loan Paid + St Loan Paid)',
              controller: userPrevTotalLoanCtrl,
            ),
          ],
        ),
        StaggeredGrid.count(
          crossAxisCount: 3,
          crossAxisSpacing: 50,
          children: [
            CustomTextfield(
              enabled: true,
              text: 'Subs * (monthly subs amt - e.g. ₹1500/ month)',
              controller: userPrevSubsCtrl,
            ),
            CustomTextfield(
              enabled: true,
              text: 'LT Installment *',
              controller: userPrevLtInstallmentCtrl,
            ),
            CustomTextfield(
              enabled: true,
              text: 'ST Installment *',
              controller: userPrevStInstallmentCtrl,
            ),
            CustomTextfield(
              enabled: true,
              text: 'Interest * (monthly int on total ST & LT loan due)',
              controller: userPrevInterestCtrl,
            ),
            CustomTextfield(
              enabled: true,
              text: 'Dues *',
              controller: duesCtrl,
            ),
            CustomTextfield(
              enabled: true,
              text: 'Penalty *',
              controller: penaltyCtrl,
            ),
            CustomTextfield(
              enabled: true,
              text:
                  'Total * (Total of monthly subs, LT & ST Instalment, int, dues, penalty)',
              controller: userPrevLoanTotalSecondCtrl,
            ),
          ],
        ),
        StaggeredGrid.count(
          crossAxisCount: 3,
          crossAxisSpacing: 50,
          children: [
            CustomTextfield(
              enabled: true,
              text: 'LT Closing Balance *',
              controller: userPrevCbltCtrl,
            ),
            CustomTextfield(
              enabled: true,
              text: 'ST Closing Balance *',
              controller: userPrevCbstCtrl,
            ),
          ],
        ),
        StaggeredGrid.count(
          crossAxisCount: 3,
          crossAxisSpacing: 50,
          children: [
            CustomTextfield(
              enabled: true,
              text: 'Installment Received *',
              controller: userPrevInstallmentReceivedCtrl,
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  "Installment Received Date * : ",
                  style: TextStyle(fontSize: 15),
                ),
                InkWell(
                  onTap: onInstallmentRecDateTap,
                  child: TextFormField(
                    style: const TextStyle(color: Colors.black),
                    enabled: false,
                    controller: TextEditingController(
                      text: installmentRecDate
                              ?.toLocal()
                              .toString()
                              .split(' ')[0] ??
                          'Select Date',
                    ),
                    decoration: InputDecoration(
                      filled: true,
                      prefixIcon: const Icon(
                        Icons.date_range,
                        size: 20,
                        color: Colors.black,
                      ),
                      border: OutlineInputBorder(
                        borderSide: BorderSide.none,
                        borderRadius: BorderRadius.circular(6),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
        StaggeredGrid.count(
          crossAxisCount: 3,
          crossAxisSpacing: 50,
          children: [
            CustomTextfield(
              enabled: true,
              text: 'Subscripiton Paid *',
              controller: userPrevSubsPaidCtrl,
            ),
            CustomTextfield(
              enabled: true,
              text: 'LT Interest Paid *',
              controller: userPrevLtInterestpaidCtrl,
            ),
            CustomTextfield(
              enabled: true,
              text: 'ST Interest Paid *',
              controller: userPrevStInterestpaidCtrl,
            ),
            CustomTextfield(
              enabled: true,
              text: 'LT Installment Paid *',
              controller: userPrevLtInstallmentpaidCtrl,
            ),
            CustomTextfield(
              enabled: true,
              text: 'ST Installment Paid *',
              controller: userPrevStInstallmentpaidCtrl,
            ),
            CustomTextfield(
              enabled: true,
              text: 'Penalty Paid *',
              controller: userPrevPenaltyPaidCtrl,
            ),
          ],
        ),
      ],
    );
  }
}

class NomineeDetailsSection extends StatelessWidget {
  final TextEditingController nomineeNamectrl;
  final TextEditingController nomineeRelationctrl;

  const NomineeDetailsSection({
    super.key,
    required this.nomineeNamectrl,
    required this.nomineeRelationctrl,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          "NOMINEE DETAILS : ",
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 20),
        StaggeredGrid.count(
          crossAxisCount: 2,
          crossAxisSpacing: 50,
          children: [
            CustomTextfield(
              enabled: true,
              text: 'Nominee Name',
              controller: nomineeNamectrl,
            ),
            CustomTextfield(
              enabled: true,
              text: 'Nominee Relation',
              controller: nomineeRelationctrl,
            ),
          ],
        ),
      ],
    );
  }
}
